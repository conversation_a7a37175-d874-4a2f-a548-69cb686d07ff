#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Amazon产品采集工具 - 完整HTTP请求版本
功能完整的Amazon产品信息采集工具，使用requests直接进行HTTP请求
支持多国Amazon站点、多线程采集、完整的产品信息提取
"""

import requests
import threading
import time
import json
import re
import random
import csv
import logging
import hashlib
import base64
from bs4 import BeautifulSoup
import pandas as pd
from urllib.parse import urljoin, urlparse, quote, unquote
import os
import sys
from datetime import datetime, timedelta
import concurrent.futures
from queue import Queue, Empty
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
from pathlib import Path
import openpyxl
from openpyxl.styles import Font, PatternFill, Alignment
import gzip
import brotli
# 移除fake_useragent依赖，使用内置用户代理列表

# 123Proxy代理配置
http_proxy = "http://u1856561711670614:<EMAIL>:36931"
https_proxy = "http://u1856561711670614:<EMAIL>:36931"
url = "https://ifconfig.me"

proxySettings = {
    "http": http_proxy,
    "https": https_proxy,
}

# 并发请求线程数
num_threads = 5
# 每个线程隔多长时间切换代理IP， 建议15 - 60秒
session_alive = 15

threads = []

def make_requests():
    session = requests.Session()
    switch_time = time.time() + session_alive  # Set end time for session
    while True:
        #后台直接控制IP切换
        #if time.time() > switch_time:
        #    session.close()
        #    session = requests.Session()
        #    switch_time = time.time() + session_alive  # Set end time for session
        try:
            response = session.get(url, proxies=proxySettings)
            print(response.text)
        except Exception as e:
            #print(f"RequestException occurred: {str(e)}")
            session.close()
            session = requests.Session()
            continue

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('amazon_scraper.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Amazon站点配置
AMAZON_DOMAINS = {
    'US': 'amazon.com',
    'UK': 'amazon.co.uk',
    'DE': 'amazon.de',
    'FR': 'amazon.fr',
    'IT': 'amazon.it',
    'ES': 'amazon.es',
    'CA': 'amazon.ca',
    'JP': 'amazon.co.jp',
    'AU': 'amazon.com.au',
    'IN': 'amazon.in',
    'BR': 'amazon.com.br',
    'MX': 'amazon.com.mx'
}

# 产品类别配置
PRODUCT_CATEGORIES = {
    'Electronics': 'electronics',
    'Books': 'books',
    'Clothing': 'fashion',
    'Home & Kitchen': 'home-garden',
    'Sports': 'sporting-goods',
    'Beauty': 'beauty',
    'Toys': 'toys-games',
    'Automotive': 'automotive'
}

class AmazonHTTPScraper:
    """完整功能的Amazon产品信息HTTP采集器"""

    def __init__(self, country='US', max_workers=5):
        self.country = country
        self.domain = AMAZON_DOMAINS.get(country, 'amazon.com')
        self.base_url = f"https://www.{self.domain}"
        self.max_workers = max_workers

        # 用户代理轮换 - 使用内置列表
        self.ua = None

        # 高级反爬虫配置
        self.session_rotation_count = 0
        self.max_requests_per_session = random.randint(5, 15)

        # 初始化会话
        self.session = requests.Session()
        self.setup_advanced_session()
        self.session.proxies = proxySettings

        # 采集结果存储
        self.products = []
        self.failed_urls = []
        self.stats = {
            'total_processed': 0,
            'successful': 0,
            'failed': 0,
            'start_time': None,
            'end_time': None
        }

        # 线程安全
        self.lock = threading.Lock()
        self.url_queue = Queue()

        logger.info(f"Amazon HTTP采集器已初始化 - 国家: {country}, 域名: {self.domain}")
        logger.info(f"使用123Proxy代理: proxy.123proxy.cn:36931")

    def setup_advanced_session(self):
        """设置高级会话配置"""
        # 设置连接池和重试策略
        from requests.adapters import HTTPAdapter
        from urllib3.util.retry import Retry

        # 自定义重试策略
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "OPTIONS"]
        )

        adapter = HTTPAdapter(
            max_retries=retry_strategy,
            pool_connections=10,
            pool_maxsize=20
        )

        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)

        # 设置高级请求头
        self.session.headers.update(self.get_headers())

        # 禁用SSL警告
        import urllib3
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

        # 设置会话cookies
        self.setup_session_cookies()

    def setup_session_cookies(self):
        """设置会话cookies以模拟真实浏览器"""
        # 添加一些常见的Amazon cookies
        common_cookies = {
            'session-id': f"{random.randint(100, 999)}-{random.randint(1000000, 9999999)}-{random.randint(1000000, 9999999)}",
            'ubid-main': f"{''.join(random.choices('0123456789ABCDEF', k=10))}",
            'x-main': f"{''.join(random.choices('abcdefghijklmnopqrstuvwxyz0123456789', k=20))}",
            'at-main': 'Atza|IwEBIK',
            'sess-at-main': 'CaseSensitiveUsername',
            'sst-main': 'Sst1|PQEHAEFgTSolnHtVOhVuFT',
        }

        for name, value in common_cookies.items():
            self.session.cookies.set(name, value, domain=f'.{self.domain}')

    def rotate_session_if_needed(self):
        """根据需要轮换会话"""
        self.session_rotation_count += 1

        if self.session_rotation_count >= self.max_requests_per_session:
            logger.info("轮换会话以避免检测")

            # 关闭旧会话
            self.session.close()

            # 创建新会话
            self.session = requests.Session()
            self.setup_advanced_session()
            self.session.proxies = proxySettings

            # 重置计数器
            self.session_rotation_count = 0
            self.max_requests_per_session = random.randint(5, 15)

            # 等待一段时间
            time.sleep(random.uniform(10, 30))
    
    def get_headers(self):
        """获取高度仿真的随机请求头 - 反爬虫优化版"""
        # 更真实的User-Agent列表，包含最新版本
        user_agents = [
            # Chrome Windows
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
            # Chrome Mac
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            # Firefox Windows
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:122.0) Gecko/20100101 Firefox/122.0',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0',
            # Firefox Mac
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:122.0) Gecko/20100101 Firefox/122.0',
            # Safari Mac
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2.1 Safari/605.1.15',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
            # Edge Windows
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36 Edg/121.0.0.0'
        ]

        user_agent = random.choice(user_agents)

        # 根据User-Agent调整其他头部
        is_chrome = 'Chrome' in user_agent and 'Edg' not in user_agent
        is_firefox = 'Firefox' in user_agent
        is_safari = 'Safari' in user_agent and 'Chrome' not in user_agent
        is_edge = 'Edg' in user_agent

        # 语言偏好
        accept_languages = [
            'en-US,en;q=0.9',
            'en-US,en;q=0.8,zh-CN;q=0.6',
            'en-GB,en-US;q=0.9,en;q=0.8',
            'en-US,en;q=0.5'
        ]

        # 基础头部
        headers = {
            'User-Agent': user_agent,
            'Accept-Language': random.choice(accept_languages),
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }

        # 根据浏览器类型设置特定头部
        if is_chrome or is_edge:
            headers.update({
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'none',
                'Sec-Fetch-User': '?1',
                'sec-ch-ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"' if 'Windows' in user_agent else '"macOS"',
                'Cache-Control': 'max-age=0'
            })
        elif is_firefox:
            headers.update({
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'none',
                'Sec-Fetch-User': '?1'
            })
        elif is_safari:
            headers.update({
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            })

        # 随机添加一些可选头部
        optional_headers = {}

        if random.random() < 0.7:  # 70%概率添加Referer
            optional_headers['Referer'] = 'https://www.google.com/'

        if random.random() < 0.5:  # 50%概率添加Origin
            optional_headers['Origin'] = f'https://www.{self.domain}'

        if random.random() < 0.3:  # 30%概率添加X-Requested-With
            optional_headers['X-Requested-With'] = 'XMLHttpRequest'

        headers.update(optional_headers)
        return headers

    def update_headers(self):
        """更新请求头"""
        self.session.headers.update(self.get_headers())

    def decompress_response(self, response):
        """解压响应内容"""
        content = response.content
        encoding = response.headers.get('content-encoding', '').lower()

        try:
            if encoding == 'gzip':
                content = gzip.decompress(content)
            elif encoding == 'br':
                content = brotli.decompress(content)
            elif encoding == 'deflate':
                content = content

            return content.decode('utf-8', errors='ignore')
        except Exception as e:
            logger.warning(f"解压响应失败: {str(e)}")
            return response.text
    
    def test_proxy_connection(self):
        """测试代理连接"""
        try:
            logger.info("测试代理连接...")

            # 尝试多个IP检测服务
            ip_services = [
                "https://ifconfig.me/ip",
                "https://api.ipify.org",
                "https://checkip.amazonaws.com",
                "https://ifconfig.me"
            ]

            for service in ip_services:
                try:
                    response = self.session.get(service, timeout=15)
                    if response.status_code == 200:
                        content = response.text.strip()

                        # 如果是HTML内容，尝试提取IP
                        if content.startswith('<!DOCTYPE') or content.startswith('<html'):
                            # 从HTML中提取IP
                            import re
                            ip_pattern = r'\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b'
                            ip_matches = re.findall(ip_pattern, content)
                            if ip_matches:
                                ip = ip_matches[0]  # 取第一个匹配的IP
                            else:
                                continue
                        else:
                            ip = content

                        # 验证IP格式
                        if self.is_valid_ip(ip):
                            logger.info(f"✅ 代理连接成功，当前IP: {ip}")
                            return True

                except Exception as e:
                    logger.debug(f"服务 {service} 失败: {str(e)}")
                    continue

            logger.error("❌ 所有IP检测服务都失败")
            return False

        except Exception as e:
            logger.error(f"❌ 代理连接错误: {str(e)}")
            return False

    def is_valid_ip(self, ip):
        """验证IP地址格式"""
        try:
            parts = ip.split('.')
            if len(parts) != 4:
                return False
            for part in parts:
                if not 0 <= int(part) <= 255:
                    return False
            return True
        except:
            return False

    def safe_request(self, url, max_retries=5, timeout=30):
        """高级反爬虫HTTP请求 - 强化版"""
        # 检查是否需要轮换会话
        self.rotate_session_if_needed()

        for attempt in range(max_retries):
            try:
                # 智能延迟策略
                if attempt == 0:
                    delay = random.uniform(3, 8)  # 首次请求较长延迟
                else:
                    delay = random.uniform(10, 25)  # 重试时更长延迟

                logger.info(f"等待 {delay:.1f} 秒后发送请求...")
                time.sleep(delay)

                # 每次重试都更新请求头和会话
                if attempt > 0:
                    self.update_headers()
                    # 重试时重新创建会话
                    if attempt > 2:
                        logger.info("重新创建会话...")
                        self.session.close()
                        self.session = requests.Session()
                        self.setup_advanced_session()
                        self.session.proxies = proxySettings

                # 添加随机查询参数来避免缓存
                parsed_url = urlparse(url)
                if '?' in url:
                    url_with_params = f"{url}&_t={int(time.time())}&_r={random.randint(1000, 9999)}"
                else:
                    url_with_params = f"{url}?_t={int(time.time())}&_r={random.randint(1000, 9999)}"

                # 模拟真实浏览器行为
                if '/s?' in url:  # 搜索页面
                    # 先访问首页建立会话
                    if attempt == 0:
                        try:
                            self.session.get(self.base_url, timeout=15)
                            time.sleep(random.uniform(1, 3))
                        except:
                            pass

                response = self.session.get(url_with_params, timeout=timeout, allow_redirects=True)

                # 处理不同状态码
                if response.status_code == 200:
                    content = self.decompress_response(response)

                    # 强化反爬虫检测
                    if self.is_blocked_response(content):
                        logger.warning(f"检测到反爬虫拦截 (尝试 {attempt + 1}/{max_retries})")

                        # 反爬虫应对策略
                        if attempt < max_retries - 1:
                            self.handle_anti_bot_detection(attempt)
                        continue

                    # 检查是否为有效的Amazon页面
                    if self.is_valid_amazon_page(content):
                        return content
                    else:
                        logger.warning(f"页面内容异常 (尝试 {attempt + 1}/{max_retries})")
                        continue

                elif response.status_code == 503:
                    logger.warning(f"服务不可用 (503) - Amazon反爬虫检测 (尝试 {attempt + 1}/{max_retries})")
                    # 503错误需要更长的等待时间
                    wait_time = random.uniform(60, 120) * (attempt + 1)  # 递增等待时间
                    logger.info(f"等待 {wait_time:.1f} 秒后重试...")
                    time.sleep(wait_time)

                    # 503错误后重新创建会话
                    logger.info("重新创建会话以绕过503错误...")
                    self.session.close()
                    self.session = requests.Session()
                    self.setup_advanced_session()
                    self.session.proxies = proxySettings
                    continue

                elif response.status_code == 429:
                    logger.warning(f"请求过于频繁 (429) (尝试 {attempt + 1}/{max_retries})")
                    time.sleep(random.uniform(60, 120))
                    continue

                elif response.status_code == 403:
                    logger.warning(f"访问被拒绝 (403) (尝试 {attempt + 1}/{max_retries})")
                    time.sleep(random.uniform(20, 40))
                    continue

                elif response.status_code in [301, 302, 307, 308]:
                    logger.info(f"重定向 ({response.status_code})")
                    continue

                else:
                    logger.warning(f"HTTP {response.status_code}: {url} (尝试 {attempt + 1}/{max_retries})")

            except requests.exceptions.Timeout:
                logger.warning(f"请求超时 (尝试 {attempt + 1}/{max_retries})")
                time.sleep(random.uniform(10, 20))

            except requests.exceptions.ConnectionError:
                logger.warning(f"连接错误 (尝试 {attempt + 1}/{max_retries})")
                time.sleep(random.uniform(10, 20))

            except Exception as e:
                logger.warning(f"请求失败: {str(e)} (尝试 {attempt + 1}/{max_retries})")
                time.sleep(random.uniform(5, 15))

        logger.error(f"所有重试失败: {url}")
        return None

    def handle_anti_bot_detection(self, attempt):
        """处理反爬虫检测"""
        strategies = [
            lambda: time.sleep(random.uniform(30, 60)),  # 长时间等待
            lambda: self.clear_session_cookies(),         # 清除cookies
            lambda: self.simulate_human_behavior(),       # 模拟人类行为
            lambda: time.sleep(random.uniform(60, 120))   # 超长等待
        ]

        strategy = strategies[min(attempt, len(strategies) - 1)]
        strategy()

    def clear_session_cookies(self):
        """清除会话cookies"""
        self.session.cookies.clear()
        logger.info("已清除会话cookies")

    def simulate_human_behavior(self):
        """模拟人类浏览行为"""
        try:
            # 访问一些常见页面
            common_pages = [
                f"{self.base_url}",
                f"{self.base_url}/gp/bestsellers",
                f"{self.base_url}/gp/new-releases"
            ]

            page = random.choice(common_pages)
            logger.info(f"模拟访问: {page}")

            response = self.session.get(page, timeout=15)
            time.sleep(random.uniform(3, 8))

        except Exception as e:
            logger.debug(f"模拟行为失败: {str(e)}")

    def is_valid_amazon_page(self, content):
        """检查是否为有效的Amazon页面"""
        amazon_indicators = [
            'amazon.com',
            'amazon.co.uk',
            'amazon.de',
            'data-asin',
            'nav-logo-sprites',
            'a-button-primary',
            's-result-item'
        ]

        content_lower = content.lower()
        return any(indicator in content_lower for indicator in amazon_indicators)

    def is_blocked_response(self, content):
        """强化版反爬虫检测"""
        if not content or len(content) < 100:
            return True

        content_lower = content.lower()

        # 常见反爬虫指示器
        blocked_indicators = [
            'robot check',
            'captcha',
            'blocked',
            'access denied',
            'cloudflare',
            'checking your browser',
            'please enable javascript',
            'unusual traffic',
            'security check',
            'verify you are human',
            'automated requests',
            'suspicious activity',
            'rate limit',
            'too many requests',
            'temporarily blocked',
            'anti-bot',
            'protection',
            'challenge',
            'verification',
            'please wait',
            'loading...',
            'redirecting',
            'just a moment',
            'one more step',
            'prove you\'re not a robot',
            'i\'m not a robot',
            'recaptcha',
            'hcaptcha',
            'turnstile',
            'perimeterx',
            'distil',
            'imperva',
            'akamai',
            'bot detection',
            'automated traffic',
            'unusual behavior'
        ]

        # 检查是否包含反爬虫指示器
        for indicator in blocked_indicators:
            if indicator in content_lower:
                logger.warning(f"检测到反爬虫指示器: {indicator}")
                return True

        # 检查页面结构异常
        structure_checks = [
            # 页面太短
            len(content) < 500,
            # 缺少基本Amazon元素
            'amazon' not in content_lower and 'amzn' not in content_lower,
            # 只有JavaScript重定向
            content_lower.count('javascript') > 5 and content_lower.count('redirect') > 2,
            # 空白页面或错误页面
            content_lower.count('error') > 3,
            # 过多的验证相关内容
            content_lower.count('verify') > 3 or content_lower.count('check') > 5
        ]

        if any(structure_checks):
            logger.warning("检测到页面结构异常")
            return True

        # 检查HTTP响应头异常（如果可用）
        return False
    
    def get_amazon_product(self, product_url):
        """获取Amazon产品信息"""
        try:
            logger.info(f"正在采集产品: {product_url}")

            content = self.safe_request(product_url)
            if not content:
                self.failed_urls.append(product_url)
                return None

            soup = BeautifulSoup(content, 'html.parser')

            # 提取产品信息
            product_info = self.extract_product_info(soup, product_url)

            if product_info:
                with self.lock:
                    self.products.append(product_info)
                    self.stats['successful'] += 1

                logger.info(f"✅ 成功采集产品: {product_info.get('title', 'Unknown')[:50]}...")
                return product_info
            else:
                logger.warning("⚠️ 未能提取到产品信息")
                self.failed_urls.append(product_url)
                with self.lock:
                    self.stats['failed'] += 1
                return None

        except Exception as e:
            logger.error(f"❌ 采集产品时出错: {str(e)}")
            self.failed_urls.append(product_url)
            with self.lock:
                self.stats['failed'] += 1
            return None
        finally:
            with self.lock:
                self.stats['total_processed'] += 1
    
    def extract_product_info(self, soup, url):
        """从HTML中提取完整的产品信息"""
        try:
            product_info = {
                'url': url,
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'domain': self.domain,
                'country': self.country
            }

            # 提取ASIN
            asin_match = re.search(r'/dp/([A-Z0-9]{10})', url)
            if asin_match:
                product_info['asin'] = asin_match.group(1)
            else:
                # 尝试从页面中提取ASIN
                asin_elem = soup.find('input', {'name': 'ASIN'})
                if asin_elem:
                    product_info['asin'] = asin_elem.get('value', '')

            # 提取标题
            title_selectors = [
                '#productTitle',
                '.product-title',
                'h1.a-size-large',
                'h1[data-automation-id="product-title"]',
                '.a-size-large.product-title-word-break'
            ]

            for selector in title_selectors:
                title_elem = soup.select_one(selector)
                if title_elem:
                    product_info['title'] = title_elem.get_text().strip()
                    break

            # 提取价格信息
            self.extract_price_info(soup, product_info)

            # 提取评分和评论
            self.extract_rating_info(soup, product_info)

            # 提取产品详情
            self.extract_product_details(soup, product_info)

            # 提取图片
            self.extract_images(soup, product_info)

            # 提取卖家信息
            self.extract_seller_info(soup, product_info)

            # 提取库存状态
            self.extract_availability(soup, product_info)

            # 提取产品描述
            self.extract_description(soup, product_info)

            # 提取产品特性
            self.extract_features(soup, product_info)

            # 提取分类信息
            self.extract_category_info(soup, product_info)

            return product_info if product_info.get('title') else None

        except Exception as e:
            logger.error(f"提取产品信息时出错: {str(e)}")
            return None
    
    def extract_price_info(self, soup, product_info):
        """提取价格信息"""
        # 当前价格
        price_selectors = [
            '.a-price.a-text-price.a-size-medium.apexPriceToPay .a-offscreen',
            '.a-price .a-offscreen',
            '.a-price-whole',
            '.a-price-range .a-offscreen',
            '[data-automation-id="product-price"] .a-offscreen',
            '.a-price.a-text-price .a-offscreen'
        ]

        for selector in price_selectors:
            price_elem = soup.select_one(selector)
            if price_elem:
                product_info['current_price'] = price_elem.get_text().strip()
                break

        # 原价
        original_price_selectors = [
            '.a-price.a-text-price .a-text-strike .a-offscreen',
            '.a-price[data-a-strike="true"] .a-offscreen',
            '.a-text-strike .a-offscreen'
        ]

        for selector in original_price_selectors:
            price_elem = soup.select_one(selector)
            if price_elem:
                product_info['original_price'] = price_elem.get_text().strip()
                break

        # 折扣信息
        discount_elem = soup.select_one('.a-size-large.a-color-price.savingPriceOverride')
        if discount_elem:
            product_info['discount'] = discount_elem.get_text().strip()

    def extract_rating_info(self, soup, product_info):
        """提取评分和评论信息"""
        # 评分
        rating_selectors = [
            '.a-icon-alt',
            '[data-hook="average-star-rating"] .a-icon-alt',
            '.a-star-medium .a-icon-alt'
        ]

        for selector in rating_selectors:
            rating_elem = soup.select_one(selector)
            if rating_elem:
                rating_text = rating_elem.get_text().strip()
                if 'out of' in rating_text or 'stars' in rating_text:
                    product_info['rating'] = rating_text
                    # 提取数字评分
                    rating_match = re.search(r'(\d+\.?\d*)', rating_text)
                    if rating_match:
                        product_info['rating_score'] = float(rating_match.group(1))
                    break

        # 评论数
        review_selectors = [
            '#acrCustomerReviewText',
            '[data-hook="total-review-count"]',
            '.a-link-normal[href*="reviews"]'
        ]

        for selector in review_selectors:
            review_elem = soup.select_one(selector)
            if review_elem:
                review_text = review_elem.get_text().strip()
                product_info['review_count'] = review_text
                # 提取数字
                review_match = re.search(r'([\d,]+)', review_text.replace(',', ''))
                if review_match:
                    product_info['review_count_num'] = int(review_match.group(1).replace(',', ''))
                break

    def extract_product_details(self, soup, product_info):
        """提取产品详细信息"""
        # 品牌
        brand_selectors = [
            '#bylineInfo',
            '.a-link-normal[href*="/brand/"]',
            '.po-brand .po-break-word'
        ]

        for selector in brand_selectors:
            brand_elem = soup.select_one(selector)
            if brand_elem:
                product_info['brand'] = brand_elem.get_text().strip()
                break

        # 产品尺寸/重量等详细信息
        detail_table = soup.find('table', {'id': 'productDetails_detailBullets_sections1'})
        if detail_table:
            details = {}
            rows = detail_table.find_all('tr')
            for row in rows:
                cells = row.find_all('td')
                if len(cells) >= 2:
                    key = cells[0].get_text().strip()
                    value = cells[1].get_text().strip()
                    details[key] = value
            product_info['product_details'] = details
    
    def extract_images(self, soup, product_info):
        """提取产品图片"""
        images = []

        # 主图片
        main_image_selectors = [
            '#landingImage',
            '#imgBlkFront',
            '.a-dynamic-image'
        ]

        for selector in main_image_selectors:
            img_elem = soup.select_one(selector)
            if img_elem:
                src = img_elem.get('src') or img_elem.get('data-src')
                if src:
                    product_info['main_image'] = src
                    images.append(src)
                break

        # 其他图片
        img_elements = soup.select('.a-button-thumbnail img')
        for img in img_elements:
            src = img.get('src') or img.get('data-src')
            if src and src not in images:
                images.append(src)

        product_info['images'] = images[:10]  # 限制图片数量

    def extract_seller_info(self, soup, product_info):
        """提取卖家信息"""
        # 卖家名称
        seller_selectors = [
            '#sellerProfileTriggerId',
            '.a-link-normal[href*="/seller/"]',
            '#merchant-info a'
        ]

        for selector in seller_selectors:
            seller_elem = soup.select_one(selector)
            if seller_elem:
                product_info['seller'] = seller_elem.get_text().strip()
                break

        # 是否Amazon发货
        fulfillment_elem = soup.find(text=re.compile(r'Ships from.*Amazon|Sold by.*Amazon'))
        if fulfillment_elem:
            product_info['fulfilled_by_amazon'] = True
        else:
            product_info['fulfilled_by_amazon'] = False

    def extract_availability(self, soup, product_info):
        """提取库存状态"""
        availability_selectors = [
            '#availability span',
            '.a-color-success',
            '.a-color-state',
            '#outOfStock'
        ]

        for selector in availability_selectors:
            avail_elem = soup.select_one(selector)
            if avail_elem:
                avail_text = avail_elem.get_text().strip()
                product_info['availability'] = avail_text

                # 判断是否有库存
                if any(word in avail_text.lower() for word in ['in stock', 'available', '有库存']):
                    product_info['in_stock'] = True
                elif any(word in avail_text.lower() for word in ['out of stock', 'unavailable', '缺货']):
                    product_info['in_stock'] = False
                break

    def extract_description(self, soup, product_info):
        """提取产品描述"""
        # 产品描述
        desc_selectors = [
            '#feature-bullets ul',
            '#productDescription',
            '.a-unordered-list.a-vertical'
        ]

        for selector in desc_selectors:
            desc_elem = soup.select_one(selector)
            if desc_elem:
                desc_text = desc_elem.get_text().strip()
                product_info['description'] = desc_text[:1000]  # 限制长度
                break

    def extract_features(self, soup, product_info):
        """提取产品特性"""
        features = []

        # 特性列表
        feature_elements = soup.select('#feature-bullets li span.a-list-item')
        for elem in feature_elements:
            feature_text = elem.get_text().strip()
            if feature_text and len(feature_text) > 10:
                features.append(feature_text)

        product_info['features'] = features[:10]  # 限制特性数量

    def extract_category_info(self, soup, product_info):
        """提取分类信息"""
        # 面包屑导航
        breadcrumb_selectors = [
            '#wayfinding-breadcrumbs_container',
            '.a-unordered-list.a-horizontal.a-size-small'
        ]

        categories = []
        for selector in breadcrumb_selectors:
            breadcrumb = soup.select_one(selector)
            if breadcrumb:
                links = breadcrumb.find_all('a')
                for link in links:
                    cat_text = link.get_text().strip()
                    if cat_text:
                        categories.append(cat_text)
                break

        product_info['categories'] = categories
    
    def search_products(self, keyword, max_pages=3, max_products_per_page=20):
        """搜索Amazon产品"""
        try:
            logger.info(f"搜索关键词: {keyword}")
            self.stats['start_time'] = datetime.now()

            search_url = f"{self.base_url}/s?k={quote(keyword)}"
            all_product_urls = []

            for page in range(1, max_pages + 1):
                logger.info(f"正在搜索第 {page} 页...")

                page_url = f"{search_url}&page={page}" if page > 1 else search_url

                content = self.safe_request(page_url)
                if not content:
                    logger.warning(f"第 {page} 页请求失败")
                    continue

                soup = BeautifulSoup(content, 'html.parser')

                # 提取产品链接
                product_links = self.extract_product_links(soup)

                logger.info(f"第 {page} 页找到 {len(product_links)} 个产品")

                # 限制每页产品数量
                product_links = product_links[:max_products_per_page]
                all_product_urls.extend(product_links)

                # 页面间延迟
                time.sleep(random.uniform(3, 6))

            logger.info(f"总共找到 {len(all_product_urls)} 个产品URL")

            # 多线程采集产品信息
            if all_product_urls:
                self.process_products_multithreaded(all_product_urls)

            self.stats['end_time'] = datetime.now()
            self.print_stats()

        except Exception as e:
            logger.error(f"搜索产品时出错: {str(e)}")

    def extract_product_links(self, soup):
        """从搜索结果页面提取产品链接"""
        links = []

        # 产品链接选择器
        link_selectors = [
            '[data-component-type="s-search-result"] h2 a',
            '.s-result-item h2 a',
            'h2.a-size-mini a',
            '.s-link-style a',
            '.a-link-normal[href*="/dp/"]'
        ]

        for selector in link_selectors:
            elements = soup.select(selector)
            for elem in elements:
                href = elem.get('href')
                if href and '/dp/' in href:
                    # 清理URL，只保留必要部分
                    if href.startswith('/'):
                        full_url = urljoin(self.base_url, href)
                    else:
                        full_url = href

                    # 提取ASIN并构建标准URL
                    asin_match = re.search(r'/dp/([A-Z0-9]{10})', full_url)
                    if asin_match:
                        asin = asin_match.group(1)
                        clean_url = f"{self.base_url}/dp/{asin}"
                        if clean_url not in links:
                            links.append(clean_url)

        return links

    def process_products_multithreaded(self, product_urls):
        """多线程处理产品URL"""
        logger.info(f"开始多线程采集 {len(product_urls)} 个产品")

        # 将URL添加到队列
        for url in product_urls:
            self.url_queue.put(url)

        # 创建线程池
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            futures = []

            for i in range(self.max_workers):
                future = executor.submit(self.worker_thread)
                futures.append(future)

            # 等待所有线程完成
            concurrent.futures.wait(futures)

        logger.info("多线程采集完成")

    def worker_thread(self):
        """工作线程函数"""
        while True:
            try:
                url = self.url_queue.get(timeout=5)
                self.get_amazon_product(url)
                self.url_queue.task_done()

                # 线程间随机延迟
                time.sleep(random.uniform(1, 3))

            except Empty:
                break
            except Exception as e:
                logger.error(f"工作线程错误: {str(e)}")
                break

    def print_stats(self):
        """打印统计信息"""
        if self.stats['start_time'] and self.stats['end_time']:
            duration = self.stats['end_time'] - self.stats['start_time']
            logger.info("=" * 50)
            logger.info("采集统计信息:")
            logger.info(f"总处理数量: {self.stats['total_processed']}")
            logger.info(f"成功采集: {self.stats['successful']}")
            logger.info(f"失败数量: {self.stats['failed']}")
            logger.info(f"成功率: {self.stats['successful']/max(self.stats['total_processed'], 1)*100:.1f}%")
            logger.info(f"耗时: {duration}")
            logger.info("=" * 50)

    def save_results(self, filename=None, format='excel'):
        """保存采集结果"""
        if not self.products:
            logger.warning("没有采集到产品数据")
            return False

        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            if format == 'excel':
                filename = f"amazon_products_{self.country}_{timestamp}.xlsx"
            elif format == 'csv':
                filename = f"amazon_products_{self.country}_{timestamp}.csv"
            elif format == 'json':
                filename = f"amazon_products_{self.country}_{timestamp}.json"

        try:
            if format == 'excel':
                self.save_to_excel(filename)
            elif format == 'csv':
                self.save_to_csv(filename)
            elif format == 'json':
                self.save_to_json(filename)

            logger.info(f"✅ 结果已保存到: {filename}")
            logger.info(f"共采集到 {len(self.products)} 个产品")
            return True

        except Exception as e:
            logger.error(f"❌ 保存结果时出错: {str(e)}")
            return False

    def save_to_excel(self, filename):
        """保存为Excel格式"""
        df = pd.DataFrame(self.products)

        # 创建Excel写入器
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='Products', index=False)

            # 获取工作表
            worksheet = writer.sheets['Products']

            # 设置列宽
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                worksheet.column_dimensions[column_letter].width = adjusted_width

            # 设置标题行样式
            for cell in worksheet[1]:
                cell.font = Font(bold=True)
                cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")

    def save_to_csv(self, filename):
        """保存为CSV格式"""
        df = pd.DataFrame(self.products)
        df.to_csv(filename, index=False, encoding='utf-8-sig')

    def save_to_json(self, filename):
        """保存为JSON格式"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.products, f, ensure_ascii=False, indent=2)

    def get_product_by_asin(self, asin):
        """根据ASIN获取单个产品信息"""
        url = f"{self.base_url}/dp/{asin}"
        return self.get_amazon_product(url)

    def get_products_by_asins(self, asins):
        """批量获取产品信息"""
        logger.info(f"开始批量采集 {len(asins)} 个ASIN")

        urls = [f"{self.base_url}/dp/{asin}" for asin in asins]
        self.process_products_multithreaded(urls)

        return self.products

class AmazonScraperGUI:
    """Amazon采集器图形界面"""

    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Amazon产品采集工具 - HTTP版本")
        self.root.geometry("800x600")

        self.scraper = None
        self.setup_ui()

    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置区域
        config_frame = ttk.LabelFrame(main_frame, text="配置", padding="10")
        config_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

        # 国家选择
        ttk.Label(config_frame, text="Amazon站点:").grid(row=0, column=0, sticky=tk.W)
        self.country_var = tk.StringVar(value="US")
        country_combo = ttk.Combobox(config_frame, textvariable=self.country_var,
                                   values=list(AMAZON_DOMAINS.keys()), state="readonly")
        country_combo.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(5, 0))

        # 线程数
        ttk.Label(config_frame, text="线程数:").grid(row=0, column=2, sticky=tk.W, padx=(20, 0))
        self.threads_var = tk.StringVar(value="3")
        threads_spin = ttk.Spinbox(config_frame, from_=1, to=10, textvariable=self.threads_var, width=5)
        threads_spin.grid(row=0, column=3, sticky=tk.W, padx=(5, 0))

        # 搜索区域
        search_frame = ttk.LabelFrame(main_frame, text="搜索", padding="10")
        search_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

        # 关键词
        ttk.Label(search_frame, text="关键词:").grid(row=0, column=0, sticky=tk.W)
        self.keyword_var = tk.StringVar()
        keyword_entry = ttk.Entry(search_frame, textvariable=self.keyword_var, width=30)
        keyword_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(5, 0))

        # 页数
        ttk.Label(search_frame, text="页数:").grid(row=0, column=2, sticky=tk.W, padx=(20, 0))
        self.pages_var = tk.StringVar(value="2")
        pages_spin = ttk.Spinbox(search_frame, from_=1, to=20, textvariable=self.pages_var, width=5)
        pages_spin.grid(row=0, column=3, sticky=tk.W, padx=(5, 0))

        # 按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=2, column=0, columnspan=2, pady=(0, 10))

        # 测试代理按钮
        test_btn = ttk.Button(button_frame, text="测试代理", command=self.test_proxy)
        test_btn.grid(row=0, column=0, padx=(0, 5))

        # 开始采集按钮
        start_btn = ttk.Button(button_frame, text="开始采集", command=self.start_scraping)
        start_btn.grid(row=0, column=1, padx=(0, 5))

        # 保存结果按钮
        save_btn = ttk.Button(button_frame, text="保存结果", command=self.save_results)
        save_btn.grid(row=0, column=2, padx=(0, 5))

        # 清空结果按钮
        clear_btn = ttk.Button(button_frame, text="清空结果", command=self.clear_results)
        clear_btn.grid(row=0, column=3)

        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="日志", padding="10")
        log_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 日志文本框
        self.log_text = scrolledtext.ScrolledText(log_frame, height=20, width=80)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(3, weight=1)
        config_frame.columnconfigure(1, weight=1)
        search_frame.columnconfigure(1, weight=1)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)

    def log_message(self, message):
        """添加日志消息"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        self.root.update()

    def test_proxy(self):
        """测试代理连接"""
        try:
            self.log_message("开始测试代理连接...")

            country = self.country_var.get()
            threads = int(self.threads_var.get())

            self.scraper = AmazonHTTPScraper(country=country, max_workers=threads)

            if self.scraper.test_proxy_connection():
                self.log_message("✅ 代理连接测试成功")
            else:
                self.log_message("❌ 代理连接测试失败")

        except Exception as e:
            self.log_message(f"❌ 测试代理时出错: {str(e)}")

    def start_scraping(self):
        """开始采集"""
        try:
            keyword = self.keyword_var.get().strip()
            if not keyword:
                messagebox.showwarning("警告", "请输入搜索关键词")
                return

            pages = int(self.pages_var.get())
            country = self.country_var.get()
            threads = int(self.threads_var.get())

            self.log_message(f"开始采集: {keyword}")
            self.log_message(f"站点: {country}, 页数: {pages}, 线程: {threads}")

            # 创建采集器
            if not self.scraper:
                self.scraper = AmazonHTTPScraper(country=country, max_workers=threads)

            # 在新线程中运行采集
            def scrape_thread():
                try:
                    self.scraper.search_products(keyword, pages)
                    self.log_message(f"✅ 采集完成，共获得 {len(self.scraper.products)} 个产品")
                except Exception as e:
                    self.log_message(f"❌ 采集过程中出错: {str(e)}")

            threading.Thread(target=scrape_thread, daemon=True).start()

        except Exception as e:
            self.log_message(f"❌ 启动采集时出错: {str(e)}")

    def save_results(self):
        """保存结果"""
        try:
            if not self.scraper or not self.scraper.products:
                messagebox.showwarning("警告", "没有可保存的数据")
                return

            filename = filedialog.asksaveasfilename(
                defaultextension=".xlsx",
                filetypes=[
                    ("Excel文件", "*.xlsx"),
                    ("CSV文件", "*.csv"),
                    ("JSON文件", "*.json")
                ]
            )

            if filename:
                if filename.endswith('.xlsx'):
                    format_type = 'excel'
                elif filename.endswith('.csv'):
                    format_type = 'csv'
                elif filename.endswith('.json'):
                    format_type = 'json'
                else:
                    format_type = 'excel'

                if self.scraper.save_results(filename, format_type):
                    self.log_message(f"✅ 结果已保存到: {filename}")
                else:
                    self.log_message("❌ 保存失败")

        except Exception as e:
            self.log_message(f"❌ 保存结果时出错: {str(e)}")

    def clear_results(self):
        """清空结果"""
        if self.scraper:
            self.scraper.products = []
            self.scraper.failed_urls = []
            self.scraper.stats = {
                'total_processed': 0,
                'successful': 0,
                'failed': 0,
                'start_time': None,
                'end_time': None
            }
        self.log_message("✅ 结果已清空")

    def run(self):
        """运行GUI"""
        self.root.mainloop()

def main():
    """主函数"""
    if len(sys.argv) > 1 and sys.argv[1] == '--gui':
        # GUI模式
        app = AmazonScraperGUI()
        app.run()
    else:
        # 命令行模式
        print("=" * 60)
        print("Amazon产品采集工具 - 完整HTTP请求版本")
        print("使用123Proxy代理进行数据采集")
        print("=" * 60)

        # 创建采集器
        country = input("请选择Amazon站点 (US/UK/DE/FR/IT/ES/CA/JP/AU/IN/BR/MX) [默认: US]: ").strip().upper()
        if country not in AMAZON_DOMAINS:
            country = 'US'

        scraper = AmazonHTTPScraper(country=country)

        # 测试代理连接
        if not scraper.test_proxy_connection():
            print("代理连接失败，请检查配置")
            return

        # 选择采集模式
        print("\n采集模式:")
        print("1. 关键词搜索")
        print("2. ASIN列表")

        mode = input("请选择模式 (1/2) [默认: 1]: ").strip()

        if mode == '2':
            # ASIN模式
            asin_input = input("请输入ASIN列表 (用逗号分隔): ").strip()
            if asin_input:
                asins = [asin.strip() for asin in asin_input.split(',')]
                print(f"\n开始采集 {len(asins)} 个ASIN")
                scraper.get_products_by_asins(asins)
        else:
            # 搜索模式
            keyword = input("请输入搜索关键词 (默认: wireless headphones): ").strip()
            if not keyword:
                keyword = "wireless headphones"

            max_pages = input("请输入搜索页数 (默认: 2): ").strip()
            try:
                max_pages = int(max_pages) if max_pages else 2
            except:
                max_pages = 2

            print(f"\n开始搜索: {keyword}")
            print(f"搜索页数: {max_pages}")
            print("-" * 60)

            # 开始采集
            scraper.search_products(keyword, max_pages)

        # 保存结果
        if scraper.products:
            save_format = input("\n保存格式 (excel/csv/json) [默认: excel]: ").strip().lower()
            if save_format not in ['excel', 'csv', 'json']:
                save_format = 'excel'

            scraper.save_results(format=save_format)

        print("\n" + "=" * 60)
        print("采集完成！")
        print("提示: 使用 --gui 参数启动图形界面")

if __name__ == "__main__":
    main()
