#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
123Proxy代理测试脚本
用于验证所有4个代码文件的代理配置是否正常工作
"""

import requests
import threading
import time
import json

# 123Proxy代理配置 - 与其他文件保持一致
PROXY_USERNAME = "u1856561711670614"
PROXY_PASSWORD = "P0BI4UunKepU"
PROXY_HOST = "proxy.123proxy.cn"
PROXY_PORT = "36931"

# 代理设置
http_proxy = f"http://{PROXY_USERNAME}:{PROXY_PASSWORD}@{PROXY_HOST}:{PROXY_PORT}"
https_proxy = f"http://{PROXY_USERNAME}:{PROXY_PASSWORD}@{PROXY_HOST}:{PROXY_PORT}"

proxySettings = {
    "http": http_proxy,
    "https": https_proxy,
}

# 测试URL
test_urls = [
    "https://ifconfig.me",
    "https://httpbin.org/ip", 
    "https://api.ipify.org?format=json",
    "https://ipinfo.io/json"
]

def test_proxy_connection():
    """测试代理连接"""
    print("=" * 60)
    print("123Proxy代理连接测试")
    print("=" * 60)
    print(f"代理服务器: {PROXY_HOST}:{PROXY_PORT}")
    print(f"用户名: {PROXY_USERNAME}")
    print(f"密码: {PROXY_PASSWORD}")
    print("-" * 60)
    
    session = requests.Session()
    
    for i, url in enumerate(test_urls, 1):
        try:
            print(f"\n测试 {i}: {url}")
            response = session.get(url, proxies=proxySettings, timeout=10)
            
            if response.status_code == 200:
                print(f"✅ 连接成功 (状态码: {response.status_code})")
                
                # 尝试解析响应
                try:
                    if 'json' in url or url.endswith('/json'):
                        data = response.json()
                        if 'ip' in data:
                            print(f"   代理IP: {data['ip']}")
                        elif 'origin' in data:
                            print(f"   代理IP: {data['origin']}")
                        else:
                            print(f"   响应: {data}")
                    else:
                        ip = response.text.strip()
                        print(f"   代理IP: {ip}")
                except:
                    print(f"   响应内容: {response.text[:100]}...")
            else:
                print(f"❌ 连接失败 (状态码: {response.status_code})")
                
        except requests.exceptions.ProxyError as e:
            print(f"❌ 代理错误: {str(e)}")
        except requests.exceptions.Timeout as e:
            print(f"❌ 连接超时: {str(e)}")
        except requests.exceptions.ConnectionError as e:
            print(f"❌ 连接错误: {str(e)}")
        except Exception as e:
            print(f"❌ 其他错误: {str(e)}")
    
    print("\n" + "=" * 60)

def test_multiple_requests():
    """测试多次请求以验证IP轮换"""
    print("测试IP轮换 (多次请求)")
    print("-" * 60)
    
    session = requests.Session()
    ips = []
    
    for i in range(5):
        try:
            print(f"请求 {i+1}/5...", end=" ")
            response = session.get("https://ifconfig.me", proxies=proxySettings, timeout=10)
            
            if response.status_code == 200:
                ip = response.text.strip()
                ips.append(ip)
                print(f"IP: {ip}")
            else:
                print(f"失败 (状态码: {response.status_code})")
                
            time.sleep(2)  # 等待2秒
            
        except Exception as e:
            print(f"错误: {str(e)}")
    
    print(f"\n获取到的IP地址: {len(set(ips))} 个不同IP")
    for ip in set(ips):
        print(f"  - {ip}")
    
    print("\n" + "=" * 60)

def test_concurrent_requests():
    """测试并发请求"""
    print("测试并发请求 (5个线程)")
    print("-" * 60)
    
    results = []
    threads = []
    
    def make_request(thread_id):
        try:
            session = requests.Session()
            response = session.get("https://ifconfig.me", proxies=proxySettings, timeout=15)
            
            if response.status_code == 200:
                ip = response.text.strip()
                results.append(f"线程{thread_id}: {ip}")
                print(f"✅ 线程{thread_id}: {ip}")
            else:
                results.append(f"线程{thread_id}: 失败 (状态码: {response.status_code})")
                print(f"❌ 线程{thread_id}: 失败 (状态码: {response.status_code})")
                
        except Exception as e:
            results.append(f"线程{thread_id}: 错误 - {str(e)}")
            print(f"❌ 线程{thread_id}: 错误 - {str(e)}")
    
    # 创建并启动线程
    for i in range(5):
        thread = threading.Thread(target=make_request, args=(i+1,))
        threads.append(thread)
        thread.start()
    
    # 等待所有线程完成
    for thread in threads:
        thread.join()
    
    print(f"\n并发测试完成，共 {len(results)} 个结果")
    print("\n" + "=" * 60)

def main():
    """主函数"""
    print("123Proxy代理配置验证工具")
    print("用于测试所有4个代码文件的代理配置")
    print()
    
    try:
        # 基础连接测试
        test_proxy_connection()
        
        # IP轮换测试
        test_multiple_requests()
        
        # 并发测试
        test_concurrent_requests()
        
        print("✅ 代理测试完成！")
        print("\n如果所有测试都通过，说明123Proxy配置正确。")
        print("现在可以运行其他4个代码文件，它们都会使用相同的代理配置。")
        
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
    except Exception as e:
        print(f"\n\n测试过程中发生错误: {str(e)}")

if __name__ == "__main__":
    main()
