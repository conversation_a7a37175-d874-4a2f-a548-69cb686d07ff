#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Amazon搜索链接 - 直接测试从Bing获得的Amazon搜索页面
"""

import requests
import time
import random
from bs4 import BeautifulSoup
import re
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 123Proxy代理配置
http_proxy = "http://u1856561711670614:<EMAIL>:36931"
https_proxy = "http://u1856561711670614:<EMAIL>:36931"

proxySettings = {
    "http": http_proxy,
    "https": https_proxy,
}

def test_amazon_search_link():
    """测试从Bing获得的Amazon搜索链接"""
    print("=" * 60)
    print("🔗 测试Amazon搜索链接")
    print("链接: https://www.amazon.com/fan/s?k=fan")
    print("=" * 60)
    
    # 创建会话
    session = requests.Session()
    session.proxies = proxySettings
    
    # 设置请求头
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Referer': 'https://www.bing.com/',  # 重要：设置来源为Bing
        'DNT': '1'
    }
    
    session.headers.update(headers)
    
    # 1. 测试代理
    print("\n1. 测试代理连接...")
    try:
        response = session.get("https://ifconfig.me/ip", timeout=15)
        if response.status_code == 200:
            ip = response.text.strip()
            print(f"✅ 代理连接成功，IP: {ip}")
        else:
            print(f"❌ 代理连接失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 代理连接错误: {str(e)}")
        return False
    
    # 2. 访问Amazon搜索链接
    print("\n2. 访问Amazon搜索链接...")
    amazon_search_url = "https://www.amazon.com/fan/s?k=fan"
    
    try:
        print(f"请求URL: {amazon_search_url}")
        print("等待 5 秒...")
        time.sleep(5)
        
        response = session.get(amazon_search_url, timeout=30)
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应大小: {len(response.text)} 字符")
        
        if response.status_code == 200:
            print("✅ Amazon搜索页面访问成功!")
            
            # 保存页面内容
            with open('amazon_search_page.html', 'w', encoding='utf-8') as f:
                f.write(response.text)
            print("📄 页面内容已保存到 amazon_search_page.html")
            
            # 分析页面内容
            return analyze_amazon_search_page(response.text)
            
        elif response.status_code == 503:
            print("❌ 503错误 - 服务不可用")
            return False
        elif response.status_code == 403:
            print("❌ 403错误 - 访问被拒绝")
            return False
        else:
            print(f"❌ 访问失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 访问错误: {str(e)}")
        return False

def analyze_amazon_search_page(html_content):
    """分析Amazon搜索页面"""
    print("\n3. 分析Amazon搜索页面...")
    
    try:
        # 检查反爬虫
        anti_bot_keywords = ['captcha', 'robot check', 'blocked', 'unusual traffic', 'verify you are human']
        content_lower = html_content.lower()
        
        detected_keywords = [kw for kw in anti_bot_keywords if kw in content_lower]
        
        if detected_keywords:
            print(f"❌ 检测到反爬虫: {', '.join(detected_keywords)}")
            return False
        
        print("✅ 未检测到反爬虫拦截")
        
        # 解析产品
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # 查找产品容器
        product_selectors = [
            'div[data-component-type="s-search-result"]',
            'div.s-result-item',
            'div[data-asin]',
            '.sg-col-inner'
        ]
        
        products = []
        for selector in product_selectors:
            products = soup.select(selector)
            if products:
                print(f"✅ 使用选择器 '{selector}' 找到 {len(products)} 个产品")
                break
        
        if not products:
            print("⚠️ 未找到产品容器，尝试查找其他元素...")
            
            # 检查页面标题
            title = soup.find('title')
            if title:
                print(f"页面标题: {title.get_text()}")
            
            # 检查是否有搜索结果文本
            search_results_text = soup.find(text=re.compile(r'results for|showing|found'))
            if search_results_text:
                print(f"搜索结果文本: {search_results_text.strip()}")
            
            # 查找所有包含ASIN的元素
            asin_elements = soup.find_all(attrs={'data-asin': True})
            print(f"找到 {len(asin_elements)} 个包含ASIN的元素")
            
            return len(asin_elements) > 0
        
        # 解析产品信息
        print(f"\n📦 解析 {len(products)} 个产品:")
        
        extracted_products = []
        for i, product in enumerate(products[:5], 1):  # 只处理前5个
            try:
                product_info = extract_product_info(product, i)
                if product_info:
                    extracted_products.append(product_info)
                    print(f"  {i}. {product_info['title'][:50]}...")
                    if product_info.get('price'):
                        print(f"     💰 价格: {product_info['price']}")
                    if product_info.get('asin'):
                        print(f"     🔖 ASIN: {product_info['asin']}")
                else:
                    print(f"  {i}. [解析失败]")
            except Exception as e:
                print(f"  {i}. [解析错误: {str(e)}]")
        
        print(f"\n✅ 成功解析 {len(extracted_products)} 个产品")
        return len(extracted_products) > 0
        
    except Exception as e:
        print(f"❌ 页面分析错误: {str(e)}")
        return False

def extract_product_info(product_elem, index):
    """提取单个产品信息"""
    try:
        info = {}
        
        # 提取ASIN
        asin = product_elem.get('data-asin')
        if asin:
            info['asin'] = asin
        
        # 提取标题
        title_selectors = ['h2 a', '.s-link-style', 'a[data-cy="title-recipe-link"]']
        for selector in title_selectors:
            title_elem = product_elem.select_one(selector)
            if title_elem:
                info['title'] = title_elem.get_text().strip()
                info['url'] = title_elem.get('href', '')
                break
        
        # 提取价格
        price_selectors = ['.a-price .a-offscreen', '.a-price-whole', '.a-price']
        for selector in price_selectors:
            price_elem = product_elem.select_one(selector)
            if price_elem:
                info['price'] = price_elem.get_text().strip()
                break
        
        # 提取评分
        rating_elem = product_elem.select_one('.a-icon-alt')
        if rating_elem:
            rating_text = rating_elem.get_text().strip()
            if 'out of' in rating_text:
                info['rating'] = rating_text
        
        return info if info.get('title') or info.get('asin') else None
        
    except Exception as e:
        logger.debug(f"提取产品信息错误: {str(e)}")
        return None

def main():
    """主函数"""
    success = test_amazon_search_link()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 Amazon搜索链接测试成功!")
        print("✅ 代理连接正常")
        print("✅ Amazon搜索页面访问成功")
        print("✅ 产品信息解析成功")
        print("✅ 通过Bing搜索绕过反爬虫策略有效")
        
        print("\n💡 关键成功要素:")
        print("- 使用Bing作为搜索入口")
        print("- 设置正确的Referer (来自Bing)")
        print("- 123Proxy代理稳定")
        print("- 合理的延迟策略")
        
        print("\n🚀 下一步:")
        print("- 可以批量处理多个搜索关键词")
        print("- 提取更多产品详细信息")
        print("- 实现完整的采集流程")
        
    else:
        print("⚠️ Amazon搜索链接测试部分成功")
        print("✅ 代理连接正常")
        print("❌ Amazon搜索页面访问受限")
        
        print("\n🔧 建议:")
        print("- 检查保存的HTML文件")
        print("- 尝试不同的请求头")
        print("- 增加更长的延迟时间")
        print("- 考虑使用浏览器自动化")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
