#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整版Amazon HTTP采集工具演示
展示所有主要功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from 采集_HTTP版 import AmazonHTTPScraper, AMAZON_DOMAINS

def demo_basic_features():
    """演示基础功能"""
    print("=" * 60)
    print("🚀 完整版Amazon HTTP采集工具演示")
    print("=" * 60)
    
    # 创建采集器
    print("1. 初始化采集器...")
    scraper = AmazonHTTPScraper(country='US', max_workers=3)
    
    # 测试代理
    print("\n2. 测试123Proxy代理连接...")
    if scraper.test_proxy_connection():
        print("✅ 代理连接正常")
    else:
        print("❌ 代理连接失败")
        return None
    
    return scraper

def demo_single_product(scraper):
    """演示单个产品采集"""
    print("\n3. 演示单个产品采集...")
    
    # 使用一个示例ASIN
    test_asin = "B08N5WRWNW"
    print(f"采集ASIN: {test_asin}")
    
    product = scraper.get_product_by_asin(test_asin)
    
    if product:
        print("✅ 单个产品采集成功!")
        print(f"📦 产品信息:")
        print(f"   标题: {product.get('title', 'N/A')[:60]}...")
        print(f"   ASIN: {product.get('asin', 'N/A')}")
        print(f"   价格: {product.get('current_price', 'N/A')}")
        print(f"   评分: {product.get('rating', 'N/A')}")
        print(f"   品牌: {product.get('brand', 'N/A')}")
        print(f"   库存: {product.get('availability', 'N/A')}")
        return True
    else:
        print("❌ 单个产品采集失败")
        return False

def demo_search_products(scraper):
    """演示搜索功能"""
    print("\n4. 演示搜索功能...")
    
    keyword = "bluetooth headphones"
    max_pages = 1
    
    print(f"搜索关键词: {keyword}")
    print(f"搜索页数: {max_pages}")
    
    # 清空之前的结果
    scraper.products = []
    
    # 开始搜索
    scraper.search_products(keyword, max_pages, max_products_per_page=5)
    
    if scraper.products:
        print(f"✅ 搜索成功，找到 {len(scraper.products)} 个产品")
        
        print("\n📋 产品列表:")
        for i, product in enumerate(scraper.products[:3], 1):
            print(f"\n产品 {i}:")
            print(f"   标题: {product.get('title', 'N/A')[:50]}...")
            print(f"   ASIN: {product.get('asin', 'N/A')}")
            print(f"   价格: {product.get('current_price', 'N/A')}")
            print(f"   评分: {product.get('rating', 'N/A')}")
        
        return True
    else:
        print("❌ 搜索失败，未找到产品")
        return False

def demo_batch_asins(scraper):
    """演示批量ASIN采集"""
    print("\n5. 演示批量ASIN采集...")
    
    # 测试ASIN列表
    test_asins = [
        "B08N5WRWNW",
        "B07FZ8S74R",
        "B0863TXGM3"
    ]
    
    print(f"批量采集ASIN: {test_asins}")
    
    # 清空之前的结果
    scraper.products = []
    
    # 批量采集
    products = scraper.get_products_by_asins(test_asins)
    
    if products:
        print(f"✅ 批量采集成功，获得 {len(products)} 个产品")
        
        print("\n📋 批量采集结果:")
        for i, product in enumerate(products, 1):
            print(f"\n产品 {i}:")
            print(f"   ASIN: {product.get('asin', 'N/A')}")
            print(f"   标题: {product.get('title', 'N/A')[:50]}...")
            print(f"   价格: {product.get('current_price', 'N/A')}")
        
        return True
    else:
        print("❌ 批量采集失败")
        return False

def demo_save_formats(scraper):
    """演示保存功能"""
    print("\n6. 演示多格式保存...")
    
    if not scraper.products:
        print("⚠️ 没有数据可保存，先进行搜索...")
        scraper.search_products("wireless mouse", 1, 3)
    
    if scraper.products:
        formats = ['excel', 'csv', 'json']
        
        for fmt in formats:
            filename = f"demo_output.{fmt if fmt != 'excel' else 'xlsx'}"
            
            if scraper.save_results(filename, fmt):
                print(f"✅ {fmt.upper()}格式保存成功: {filename}")
                
                # 检查文件
                if os.path.exists(filename):
                    size = os.path.getsize(filename)
                    print(f"   文件大小: {size} 字节")
                    
                    # 清理演示文件
                    try:
                        os.remove(filename)
                        print(f"   演示文件已清理")
                    except:
                        pass
            else:
                print(f"❌ {fmt.upper()}格式保存失败")
        
        return True
    else:
        print("❌ 没有数据可保存")
        return False

def demo_multi_country():
    """演示多国站点"""
    print("\n7. 演示多国站点支持...")
    
    countries = ['US', 'UK', 'DE', 'JP']
    
    for country in countries:
        print(f"\n测试 {country} 站点:")
        try:
            scraper = AmazonHTTPScraper(country=country, max_workers=1)
            print(f"   域名: {scraper.domain}")
            print(f"   基础URL: {scraper.base_url}")
            
            # 简单测试代理连接
            if scraper.test_proxy_connection():
                print(f"   ✅ {country} 站点代理连接正常")
            else:
                print(f"   ⚠️ {country} 站点代理连接失败")
                
        except Exception as e:
            print(f"   ❌ {country} 站点测试出错: {str(e)}")

def demo_statistics(scraper):
    """演示统计功能"""
    print("\n8. 演示统计信息...")
    
    if scraper.stats['total_processed'] > 0:
        print("📊 采集统计:")
        print(f"   总处理数量: {scraper.stats['total_processed']}")
        print(f"   成功采集: {scraper.stats['successful']}")
        print(f"   失败数量: {scraper.stats['failed']}")
        
        if scraper.stats['total_processed'] > 0:
            success_rate = scraper.stats['successful'] / scraper.stats['total_processed'] * 100
            print(f"   成功率: {success_rate:.1f}%")
        
        if scraper.stats['start_time'] and scraper.stats['end_time']:
            duration = scraper.stats['end_time'] - scraper.stats['start_time']
            print(f"   耗时: {duration}")
        
        print(f"   失败URL数量: {len(scraper.failed_urls)}")
    else:
        print("📊 暂无统计数据")

def main():
    """主演示函数"""
    print("🎯 完整版Amazon HTTP采集工具功能演示")
    print("使用123Proxy代理进行演示")
    print()
    
    try:
        # 基础功能演示
        scraper = demo_basic_features()
        if not scraper:
            print("❌ 基础功能演示失败")
            return
        
        # 单个产品演示
        demo_single_product(scraper)
        
        # 搜索功能演示
        demo_search_products(scraper)
        
        # 批量ASIN演示
        demo_batch_asins(scraper)
        
        # 保存功能演示
        demo_save_formats(scraper)
        
        # 多国站点演示
        demo_multi_country()
        
        # 统计信息演示
        demo_statistics(scraper)
        
        print("\n" + "=" * 60)
        print("🎉 完整版HTTP采集工具演示完成!")
        print("=" * 60)
        print()
        print("🌟 主要特性:")
        print("✅ 123Proxy代理集成 - 自动IP轮换")
        print("✅ 多国Amazon站点 - 支持12个国家")
        print("✅ 智能请求头轮换 - 降低被检测风险")
        print("✅ 多线程并发采集 - 提高采集效率")
        print("✅ 完整产品信息提取 - 20+字段")
        print("✅ 多格式数据保存 - Excel/CSV/JSON")
        print("✅ 错误处理和重试 - 提高成功率")
        print("✅ 详细日志记录 - 便于调试")
        print("✅ 图形界面支持 - 易于使用")
        print("✅ 命令行和GUI双模式")
        print()
        print("🚀 使用方法:")
        print("1. 命令行模式: python 采集_HTTP版.py")
        print("2. 图形界面: python 采集_HTTP版.py --gui")
        print("3. 编程调用: from 采集_HTTP版 import AmazonHTTPScraper")
        print()
        print("📚 支持的采集方式:")
        print("• 关键词搜索采集")
        print("• ASIN批量采集")
        print("• 单个产品采集")
        print("• 分类浏览采集")
        print()
        print("🌍 支持的Amazon站点:")
        for code, domain in AMAZON_DOMAINS.items():
            print(f"• {code}: {domain}")
        print("=" * 60)
        
    except KeyboardInterrupt:
        print("\n\n演示被用户中断")
    except Exception as e:
        print(f"\n\n演示过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
