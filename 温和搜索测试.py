#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
温和搜索测试 - 使用更保守的策略搜索"fan"
模拟真实用户行为，避免触发反爬虫
"""

import requests
import time
import random
from bs4 import BeautifulSoup
import re

# 123Proxy代理配置
http_proxy = "http://u1856561711670614:<EMAIL>:36931"
https_proxy = "http://u1856561711670614:<EMAIL>:36931"

proxySettings = {
    "http": http_proxy,
    "https": https_proxy,
}

def create_session():
    """创建模拟真实浏览器的会话"""
    session = requests.Session()
    session.proxies = proxySettings
    
    # 设置真实浏览器请求头
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'Cache-Control': 'max-age=0',
        'DNT': '1'
    }
    
    session.headers.update(headers)
    return session

def simulate_human_browsing(session):
    """模拟人类浏览行为"""
    print("👤 模拟人类浏览行为...")
    
    # 1. 先访问Amazon首页
    print("  1. 访问Amazon首页...")
    try:
        response = session.get("https://www.amazon.com", timeout=20)
        if response.status_code == 200:
            print(f"     ✅ 首页访问成功 ({len(response.text)} 字符)")
            time.sleep(random.uniform(3, 6))  # 模拟阅读时间
        else:
            print(f"     ❌ 首页访问失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"     ❌ 首页访问错误: {str(e)}")
        return False
    
    # 2. 访问一个分类页面
    print("  2. 访问分类页面...")
    try:
        category_url = "https://www.amazon.com/gp/bestsellers"
        response = session.get(category_url, timeout=20)
        if response.status_code == 200:
            print(f"     ✅ 分类页面访问成功")
            time.sleep(random.uniform(2, 4))
        else:
            print(f"     ❌ 分类页面访问失败: {response.status_code}")
    except Exception as e:
        print(f"     ❌ 分类页面访问错误: {str(e)}")
    
    # 3. 模拟在首页停留
    print("  3. 模拟浏览停留...")
    time.sleep(random.uniform(5, 10))
    
    return True

def gentle_search(session, keyword="fan"):
    """温和的搜索方法"""
    print(f"\n🔍 温和搜索关键词: {keyword}")
    
    # 方法1: 通过首页搜索框
    print("  方法1: 模拟首页搜索...")
    try:
        # 先获取首页，模拟用户在搜索框输入
        print("    获取首页...")
        response = session.get("https://www.amazon.com", timeout=20)
        
        if response.status_code != 200:
            print(f"    ❌ 首页获取失败: {response.status_code}")
            return False
        
        # 模拟用户思考和输入时间
        print(f"    模拟输入关键词: {keyword}")
        time.sleep(random.uniform(2, 5))
        
        # 构建搜索URL (模拟表单提交)
        search_url = f"https://www.amazon.com/s?k={keyword}&ref=nb_sb_noss"
        
        # 设置Referer为首页
        session.headers.update({
            'Referer': 'https://www.amazon.com/'
        })
        
        print(f"    发送搜索请求...")
        response = session.get(search_url, timeout=30)
        
        print(f"    响应状态: {response.status_code}")
        print(f"    响应大小: {len(response.text)} 字符")
        
        if response.status_code == 200:
            return analyze_search_results(response.text, keyword)
        elif response.status_code == 503:
            print("    ⚠️ 服务暂时不可用，尝试其他方法...")
            return False
        else:
            print(f"    ❌ 搜索失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"    ❌ 搜索错误: {str(e)}")
        return False

def analyze_search_results(content, keyword):
    """分析搜索结果"""
    print("  📊 分析搜索结果...")
    
    # 检查反爬虫
    anti_bot_indicators = [
        'robot check', 'captcha', 'blocked', 'unusual traffic', 
        'verify you are human', 'security check', 'access denied'
    ]
    
    content_lower = content.lower()
    detected = [indicator for indicator in anti_bot_indicators if indicator in content_lower]
    
    if detected:
        print(f"    ❌ 检测到反爬虫: {', '.join(detected)}")
        
        # 保存页面用于分析
        with open(f'blocked_search_{keyword}.html', 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"    📄 页面已保存到 blocked_search_{keyword}.html")
        return False
    
    # 检查搜索结果
    try:
        soup = BeautifulSoup(content, 'html.parser')
        
        # 查找产品
        products = soup.find_all('div', {'data-component-type': 's-search-result'})
        if not products:
            products = soup.find_all('div', class_='s-result-item')
        if not products:
            products = soup.find_all('div', class_='sg-col-inner')
        
        print(f"    📦 找到 {len(products)} 个产品结果")
        
        if len(products) > 0:
            print("    ✅ 搜索成功!")
            
            # 显示前几个产品
            print("    🏷️ 产品示例:")
            count = 0
            for product in products[:5]:
                try:
                    # 查找标题
                    title_elem = product.find('h2') or product.find('a', {'class': 's-link-style'})
                    if title_elem:
                        title_link = title_elem.find('a') if title_elem.name != 'a' else title_elem
                        if title_link:
                            title = title_link.get_text().strip()
                            if title and len(title) > 10:  # 过滤掉太短的标题
                                count += 1
                                print(f"      {count}. {title[:60]}...")
                                
                                # 查找价格
                                price_elem = product.find('span', class_='a-price-whole') or product.find('span', class_='a-price')
                                if price_elem:
                                    price = price_elem.get_text().strip()
                                    print(f"         💰 价格: {price}")
                except:
                    continue
            
            if count > 0:
                print(f"    ✅ 成功解析 {count} 个有效产品")
                return True
            else:
                print("    ⚠️ 未能解析出有效产品")
                return False
        else:
            print("    ⚠️ 未找到产品结果")
            
            # 检查是否有"无结果"提示
            no_results_indicators = ['no results', 'did not match', '没有找到', 'try different keywords']
            if any(indicator in content_lower for indicator in no_results_indicators):
                print("    ℹ️ 显示无搜索结果")
            else:
                print("    ⚠️ 页面结构可能异常")
            
            return False
            
    except Exception as e:
        print(f"    ❌ 结果解析错误: {str(e)}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🕊️ 温和搜索测试")
    print("策略: 模拟真实用户行为")
    print("关键词: fan")
    print("=" * 60)
    
    # 创建会话
    session = create_session()
    
    # 测试代理
    print("🔗 测试代理连接...")
    try:
        response = session.get("https://ifconfig.me/ip", timeout=15)
        if response.status_code == 200:
            ip = response.text.strip()
            print(f"✅ 代理连接成功，IP: {ip}")
        else:
            print(f"❌ 代理测试失败: {response.status_code}")
            return
    except Exception as e:
        print(f"❌ 代理连接错误: {str(e)}")
        return
    
    # 模拟人类浏览
    if not simulate_human_browsing(session):
        print("❌ 模拟浏览失败")
        return
    
    # 温和搜索
    search_success = gentle_search(session, "fan")
    
    # 总结
    print("\n" + "=" * 60)
    if search_success:
        print("🎉 温和搜索测试成功!")
        print("✅ 代理连接正常")
        print("✅ 人类行为模拟有效")
        print("✅ 搜索功能正常")
        print("✅ 产品解析成功")
        
        print("\n💡 成功要素:")
        print("- 模拟真实用户浏览路径")
        print("- 合理的页面停留时间")
        print("- 正确的Referer设置")
        print("- 温和的请求频率")
        
    else:
        print("⚠️ 温和搜索测试部分成功")
        print("✅ 代理连接正常")
        print("✅ 基础页面访问正常")
        print("❌ 搜索功能仍受限")
        
        print("\n🔧 进一步优化建议:")
        print("- 增加更长的等待时间")
        print("- 尝试不同的搜索入口")
        print("- 使用浏览器自动化工具")
        print("- 分析保存的HTML文件")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
