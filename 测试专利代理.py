#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试专利.py的代理配置
"""

import requests
import threading
import time

# 从专利.py复制的代理配置
http_proxy = "http://u1856561711670614:<EMAIL>:36931"
https_proxy = "http://u1856561711670614:<EMAIL>:36931"
url = "https://ifconfig.me"

proxySettings = {
    "http": http_proxy,
    "https": https_proxy,
}

# 并发请求线程数
num_threads = 5
# 每个线程隔多长时间切换代理IP， 建议15 - 60秒
session_alive = 15

threads = []

def make_requests():
    session = requests.Session()
    switch_time = time.time() + session_alive  # Set end time for session
    while True:
        #后台直接控制IP切换
        #if time.time() > switch_time:
        #    session.close()
        #    session = requests.Session()
        #    switch_time = time.time() + session_alive  # Set end time for session
        try:
            response = session.get(url, proxies=proxySettings)
            print(f"线程获取IP: {response.text.strip()}")
            time.sleep(5)  # 每5秒请求一次
        except Exception as e:
            print(f"请求异常: {str(e)}")
            session.close()
            session = requests.Session()
            continue

def test_single_request():
    """测试单次请求"""
    print("=" * 50)
    print("测试专利.py的代理配置")
    print("=" * 50)
    
    try:
        session = requests.Session()
        response = session.get(url, proxies=proxySettings, timeout=10)
        if response.status_code == 200:
            print(f"✅ 代理连接成功!")
            print(f"当前IP: {response.text.strip()}")
            print(f"代理服务器: proxy.123proxy.cn:36931")
            return True
        else:
            print(f"❌ 请求失败，状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 连接失败: {str(e)}")
        return False

def test_multiple_requests():
    """测试多次请求验证IP轮换"""
    print("\n" + "=" * 50)
    print("测试IP轮换 (5次请求)")
    print("=" * 50)
    
    ips = []
    for i in range(5):
        try:
            session = requests.Session()
            response = session.get(url, proxies=proxySettings, timeout=10)
            if response.status_code == 200:
                ip = response.text.strip()
                ips.append(ip)
                print(f"请求 {i+1}: {ip}")
            else:
                print(f"请求 {i+1}: 失败 (状态码: {response.status_code})")
            time.sleep(2)
        except Exception as e:
            print(f"请求 {i+1}: 错误 - {str(e)}")
    
    unique_ips = set(ips)
    print(f"\n获取到 {len(unique_ips)} 个不同的IP地址:")
    for ip in unique_ips:
        print(f"  - {ip}")

def test_concurrent_requests():
    """测试并发请求"""
    print("\n" + "=" * 50)
    print("测试并发请求 (3个线程，每个线程运行10秒)")
    print("=" * 50)
    
    def worker(thread_id):
        session = requests.Session()
        start_time = time.time()
        request_count = 0
        
        while time.time() - start_time < 10:  # 运行10秒
            try:
                response = session.get(url, proxies=proxySettings, timeout=5)
                if response.status_code == 200:
                    ip = response.text.strip()
                    request_count += 1
                    print(f"线程{thread_id} 请求{request_count}: {ip}")
                time.sleep(2)
            except Exception as e:
                print(f"线程{thread_id} 错误: {str(e)}")
                session.close()
                session = requests.Session()
    
    threads = []
    for i in range(3):
        thread = threading.Thread(target=worker, args=(i+1,))
        threads.append(thread)
        thread.start()
    
    for thread in threads:
        thread.join()
    
    print("并发测试完成")

if __name__ == "__main__":
    print("专利.py 代理配置测试工具")
    print("使用与专利.py相同的代理配置进行测试\n")
    
    # 单次请求测试
    if test_single_request():
        # 多次请求测试
        test_multiple_requests()
        
        # 并发请求测试
        test_concurrent_requests()
        
        print("\n" + "=" * 50)
        print("✅ 专利.py 代理配置测试完成!")
        print("如果看到不同的IP地址，说明代理配置正确")
        print("现在可以运行专利.py，它将使用相同的代理配置")
        print("=" * 50)
    else:
        print("\n❌ 代理配置测试失败，请检查:")
        print("1. 网络连接是否正常")
        print("2. 123Proxy账户是否有剩余流量")
        print("3. 代理服务器地址是否正确")
