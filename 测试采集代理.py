#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试采集.py的代理配置
"""

import requests
import threading
import time

# 从采集.py复制的代理配置
http_proxy = "http://u1856561711670614:<EMAIL>:36931"
https_proxy = "http://u1856561711670614:<EMAIL>:36931"
url = "https://ifconfig.me"

proxySettings = {
    "http": http_proxy,
    "https": https_proxy,
}

# 并发请求线程数
num_threads = 5
# 每个线程隔多长时间切换代理IP， 建议15 - 60秒
session_alive = 15

threads = []

def make_requests():
    session = requests.Session()
    switch_time = time.time() + session_alive  # Set end time for session
    while True:
        #后台直接控制IP切换
        #if time.time() > switch_time:
        #    session.close()
        #    session = requests.Session()
        #    switch_time = time.time() + session_alive  # Set end time for session
        try:
            response = session.get(url, proxies=proxySettings)
            print(f"采集模块获取IP: {response.text.strip()}")
            time.sleep(5)  # 每5秒请求一次
        except Exception as e:
            print(f"采集模块请求异常: {str(e)}")
            session.close()
            session = requests.Session()
            continue

def test_scraper_proxy():
    """测试采集.py的代理配置"""
    print("=" * 60)
    print("测试采集.py的123Proxy代理配置")
    print("=" * 60)
    print(f"代理服务器: proxy.123proxy.cn:36931")
    print(f"用户名: u1856561711670614")
    print("-" * 60)
    
    try:
        print("正在测试采集模块的代理连接...")
        session = requests.Session()
        
        # 测试连接
        response = session.get("https://ifconfig.me", proxies=proxySettings, timeout=15)
        
        if response.status_code == 200:
            ip = response.text.strip()
            print(f"✅ 采集模块代理连接成功!")
            print(f"当前IP地址: {ip}")
            
            # 测试Amazon连接
            print("\n测试Amazon网站连接...")
            try:
                amazon_response = session.get("https://www.amazon.com", 
                                            proxies=proxySettings, 
                                            timeout=15,
                                            headers={
                                                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
                                            })
                if amazon_response.status_code == 200:
                    print(f"✅ Amazon连接成功 (状态码: {amazon_response.status_code})")
                else:
                    print(f"⚠️ Amazon连接异常 (状态码: {amazon_response.status_code})")
            except Exception as e:
                print(f"❌ Amazon连接失败: {str(e)}")
            
            # 测试多次请求
            print("\n测试连续3次IP请求...")
            ips = []
            for i in range(3):
                try:
                    resp = session.get("https://ifconfig.me", proxies=proxySettings, timeout=10)
                    if resp.status_code == 200:
                        current_ip = resp.text.strip()
                        ips.append(current_ip)
                        print(f"请求 {i+1}: {current_ip}")
                    time.sleep(2)
                except Exception as e:
                    print(f"请求 {i+1}: 失败 - {str(e)}")
            
            unique_ips = set(ips)
            print(f"\n获取到 {len(unique_ips)} 个不同IP:")
            for ip in unique_ips:
                print(f"  - {ip}")
            
            return True
            
        else:
            print(f"❌ 采集模块请求失败，HTTP状态码: {response.status_code}")
            return False
            
    except requests.exceptions.ProxyError as e:
        print(f"❌ 采集模块代理连接错误: {str(e)}")
        return False
        
    except requests.exceptions.Timeout as e:
        print(f"❌ 采集模块连接超时: {str(e)}")
        return False
        
    except Exception as e:
        print(f"❌ 采集模块其他错误: {str(e)}")
        return False

def test_concurrent_scraper_requests():
    """测试采集模块的并发请求"""
    print("\n" + "=" * 60)
    print("测试采集模块并发请求 (3个线程)")
    print("=" * 60)
    
    results = []
    
    def worker(thread_id):
        try:
            session = requests.Session()
            response = session.get("https://ifconfig.me", proxies=proxySettings, timeout=10)
            
            if response.status_code == 200:
                ip = response.text.strip()
                result = f"采集线程{thread_id}: {ip}"
                results.append(result)
                print(f"✅ {result}")
            else:
                result = f"采集线程{thread_id}: 失败 (状态码: {response.status_code})"
                results.append(result)
                print(f"❌ {result}")
                
        except Exception as e:
            result = f"采集线程{thread_id}: 错误 - {str(e)}"
            results.append(result)
            print(f"❌ {result}")
    
    threads = []
    for i in range(3):
        thread = threading.Thread(target=worker, args=(i+1,))
        threads.append(thread)
        thread.start()
    
    for thread in threads:
        thread.join()
    
    print(f"\n采集模块并发测试完成，共 {len(results)} 个结果")
    return len(results) > 0

def test_selenium_proxy_config():
    """测试Selenium代理配置信息"""
    print("\n" + "=" * 60)
    print("Selenium浏览器代理配置信息")
    print("=" * 60)
    
    print("Chrome浏览器代理配置:")
    print(f"  --proxy-server={http_proxy}")
    print()
    
    print("Firefox浏览器代理配置:")
    print(f"  network.proxy.type: 1")
    print(f"  network.proxy.http: proxy.123proxy.cn")
    print(f"  network.proxy.http_port: 36931")
    print(f"  network.proxy.ssl: proxy.123proxy.cn")
    print(f"  network.proxy.ssl_port: 36931")
    print(f"  network.proxy.username: u1856561711670614")
    print(f"  network.proxy.password: P0BI4UunKepU")
    print()
    
    print("✅ 采集.py中的Selenium浏览器已配置123Proxy代理")
    print("当运行采集.py时，Chrome和Firefox都会使用代理")

if __name__ == "__main__":
    print("采集.py 123Proxy代理配置测试工具")
    print("使用与采集.py相同的代理配置进行测试\n")
    
    # 基础测试
    success = test_scraper_proxy()
    
    if success:
        # 并发测试
        test_concurrent_scraper_requests()
        
        # Selenium配置信息
        test_selenium_proxy_config()
        
        print("\n" + "=" * 60)
        print("✅ 采集.py 代理配置测试完成!")
        print("采集模块已成功配置123Proxy代理")
        print("所有HTTP请求和Selenium浏览器都会通过代理服务器")
        print("=" * 60)
    else:
        print("\n" + "=" * 60)
        print("❌ 采集.py 代理配置测试失败!")
        print("请检查网络连接和123Proxy账户状态")
        print("=" * 60)
