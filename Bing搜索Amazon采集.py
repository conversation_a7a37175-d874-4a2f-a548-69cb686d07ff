#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Bing搜索Amazon采集工具 - HTTP版本
通过Bing搜索获取Amazon产品信息，绕过Amazon反爬虫
"""

import requests
import time
import random
import re
from bs4 import BeautifulSoup
from urllib.parse import urlparse, parse_qs, unquote
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 123Proxy代理配置
http_proxy = "http://u1856561711670614:<EMAIL>:36931"
https_proxy = "http://u1856561711670614:<EMAIL>:36931"

proxySettings = {
    "http": http_proxy,
    "https": https_proxy,
}

class BingAmazonScraper:
    """通过Bing搜索Amazon产品的采集器"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.proxies = proxySettings
        
        # 数据结构 - 优化搜索查询
        self.countries = {
            "美国": {"domain": "amazon.com", "query_format": "site:amazon.com '{keyword}'", "lang": "en"},
            "加拿大": {"domain": "amazon.ca", "query_format": "site:amazon.ca '{keyword}'", "lang": "en"},
            "日本": {"domain": "amazon.co.jp", "query_format": "site:amazon.co.jp '{keyword}'", "lang": "ja"}
        }
        
        self.all_amazon_codes = []
        self.setup_session()
    
    def setup_session(self):
        """设置会话"""
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'DNT': '1'
        }
        
        self.session.headers.update(headers)
        logger.info("会话已设置")
    
    def test_proxy(self):
        """测试代理连接"""
        try:
            response = self.session.get("https://ifconfig.me/ip", timeout=15)
            if response.status_code == 200:
                ip = response.text.strip()
                logger.info(f"代理连接成功，IP: {ip}")
                return True
            return False
        except Exception as e:
            logger.error(f"代理连接失败: {str(e)}")
            return False
    
    def search_bing_for_amazon(self, keyword, country="美国", max_pages=3):
        """通过Bing搜索Amazon产品"""
        logger.info(f"开始Bing搜索: {keyword} (国家: {country})")
        
        if country not in self.countries:
            logger.error(f"不支持的国家: {country}")
            return []
        
        country_info = self.countries[country]
        search_query = country_info["query_format"].format(keyword=keyword)
        
        logger.info(f"搜索查询: {search_query}")
        
        all_amazon_links = []
        
        for page in range(max_pages):
            logger.info(f"搜索第 {page + 1} 页...")
            
            # 构建Bing搜索URL
            if page == 0:
                search_url = f"https://www.bing.com/search?q={requests.utils.quote(search_query)}"
            else:
                first_param = page * 10  # Bing每页10个结果
                search_url = f"https://www.bing.com/search?q={requests.utils.quote(search_query)}&first={first_param}"
            
            logger.info(f"请求URL: {search_url}")
            
            # 发送请求
            try:
                # 随机延迟
                time.sleep(random.uniform(2, 5))
                
                response = self.session.get(search_url, timeout=30)
                
                if response.status_code == 200:
                    logger.info(f"Bing搜索成功，页面大小: {len(response.text)}")

                    # 保存搜索结果用于调试
                    if page == 0:  # 只保存第一页
                        with open(f'bing_search_results_{keyword}.html', 'w', encoding='utf-8') as f:
                            f.write(response.text)
                        logger.info(f"搜索结果已保存到 bing_search_results_{keyword}.html")

                    # 解析搜索结果
                    amazon_links = self.parse_bing_results(response.text, country_info["domain"])
                    all_amazon_links.extend(amazon_links)

                    logger.info(f"第 {page + 1} 页找到 {len(amazon_links)} 个Amazon链接")
                    
                else:
                    logger.warning(f"Bing搜索失败，状态码: {response.status_code}")
                    break
                    
            except Exception as e:
                logger.error(f"Bing搜索出错: {str(e)}")
                break
        
        logger.info(f"总共找到 {len(all_amazon_links)} 个Amazon链接")
        return all_amazon_links
    
    def parse_bing_results(self, html_content, target_domain):
        """解析Bing搜索结果，提取Amazon链接"""
        amazon_links = []

        try:
            soup = BeautifulSoup(html_content, 'html.parser')

            # 先检查是否有搜索结果
            all_links = soup.find_all('a', href=True)
            logger.info(f"页面中总共找到 {len(all_links)} 个链接")

            # 统计包含amazon的链接
            amazon_related_links = [link for link in all_links if 'amazon' in link.get('href', '').lower()]
            logger.info(f"包含'amazon'的链接: {len(amazon_related_links)}")

            # Bing搜索结果的选择器
            result_selectors = [
                'li.b_algo h2 a',  # 主要结果
                'li.b_algo a',     # 所有链接
                '.b_title a',      # 标题链接
                'a[href*="amazon"]'  # 包含amazon的链接
            ]

            for i, selector in enumerate(result_selectors):
                links = soup.select(selector)
                logger.info(f"选择器 {i+1} '{selector}' 找到 {len(links)} 个链接")

                for link in links:
                    href = link.get('href', '')
                    text = link.get_text().strip()

                    if href:
                        logger.debug(f"检查链接: {href[:100]}...")

                        # 处理Amazon链接
                        amazon_link = self.extract_amazon_link(href, target_domain)
                        if amazon_link and amazon_link not in amazon_links:
                            amazon_links.append(amazon_link)
                            logger.info(f"找到Amazon链接: {amazon_link}")

                            # 提取产品代码
                            code = self.extract_amazon_code(amazon_link)
                            if code:
                                logger.info(f"找到Amazon产品代码: {code}")
                                if code not in self.all_amazon_codes:
                                    self.all_amazon_codes.append(code)

            # 如果没找到，尝试更宽泛的搜索
            if not amazon_links:
                logger.info("尝试更宽泛的Amazon链接搜索...")
                for link in all_links:
                    href = link.get('href', '')
                    if target_domain in href:
                        logger.info(f"发现目标域名链接: {href}")
                        amazon_link = self.extract_amazon_link(href, target_domain)
                        if amazon_link and amazon_link not in amazon_links:
                            amazon_links.append(amazon_link)
                            logger.info(f"成功提取Amazon链接: {amazon_link}")

                            # 提取产品代码
                            code = self.extract_amazon_code(amazon_link)
                            if code:
                                logger.info(f"找到Amazon产品代码: {code}")
                                if code not in self.all_amazon_codes:
                                    self.all_amazon_codes.append(code)

        except Exception as e:
            logger.error(f"解析Bing结果出错: {str(e)}")

        return amazon_links
    
    def extract_amazon_link(self, href, target_domain):
        """提取Amazon链接"""
        try:
            # 直接Amazon链接 - 扩展匹配条件
            if target_domain in href:
                # 检查是否为产品页面或搜索页面
                if '/dp/' in href or '/s?' in href or '/gp/product/' in href:
                    logger.info(f"找到直接Amazon链接: {href}")
                    return href

            # Bing重定向链接处理
            if 'bing.com/ck' in href:
                url_parts = urlparse(href)
                query = parse_qs(url_parts.query)
                encoded_amazon_url = query.get('u', [None])[0]

                if encoded_amazon_url:
                    try:
                        # 解码URL
                        import base64
                        decoded_url = base64.b64decode(encoded_amazon_url + '==').decode('utf-8')
                        if target_domain in decoded_url:
                            logger.info(f"找到Bing重定向Amazon链接: {decoded_url}")
                            return decoded_url
                    except:
                        # 如果base64解码失败，尝试URL解码
                        decoded_url = unquote(encoded_amazon_url)
                        if target_domain in decoded_url:
                            logger.info(f"找到URL解码Amazon链接: {decoded_url}")
                            return decoded_url

            # URL编码的Amazon链接
            if 'amazon' in href and '%' in href:
                decoded_href = unquote(href)
                if target_domain in decoded_href:
                    logger.info(f"找到URL编码Amazon链接: {decoded_href}")
                    return decoded_href

            # 检查是否为相对链接但包含Amazon域名
            if href.startswith('/') and 'amazon' in href:
                # 可能是相对链接，需要进一步处理
                logger.debug(f"发现可能的Amazon相对链接: {href}")

        except Exception as e:
            logger.debug(f"提取Amazon链接出错: {str(e)}")

        return None
    
    def extract_amazon_code(self, amazon_url):
        """从Amazon URL中提取产品代码(ASIN)"""
        try:
            # 匹配Amazon产品代码模式
            patterns = [
                r'/dp/([A-Z0-9]{10})',  # 标准ASIN格式
                r'/gp/product/([A-Z0-9]{10})',  # 另一种格式
                r'asin=([A-Z0-9]{10})',  # 查询参数格式
            ]

            for pattern in patterns:
                match = re.search(pattern, amazon_url)
                if match:
                    return match.group(1)

            # 如果是搜索页面，返回搜索关键词作为标识
            if '/s?' in amazon_url:
                # 提取搜索关键词
                search_match = re.search(r'[?&]k=([^&]+)', amazon_url)
                if search_match:
                    keyword = unquote(search_match.group(1))
                    logger.info(f"找到Amazon搜索页面，关键词: {keyword}")
                    return f"SEARCH_{keyword}"

        except Exception as e:
            logger.debug(f"提取产品代码出错: {str(e)}")

        return None
    
    def get_amazon_product_info(self, asin, domain="amazon.com"):
        """获取Amazon产品详细信息"""
        product_url = f"https://www.{domain}/dp/{asin}"
        
        try:
            logger.info(f"获取产品信息: {asin}")
            
            # 随机延迟
            time.sleep(random.uniform(3, 6))
            
            response = self.session.get(product_url, timeout=30)
            
            if response.status_code == 200:
                return self.parse_amazon_product(response.text, asin, product_url)
            else:
                logger.warning(f"获取产品信息失败: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"获取产品信息出错: {str(e)}")
            return None
    
    def parse_amazon_product(self, html_content, asin, url):
        """解析Amazon产品页面"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            product_info = {
                'asin': asin,
                'url': url,
                'title': '',
                'price': '',
                'rating': '',
                'reviews': ''
            }
            
            # 提取标题
            title_selectors = ['#productTitle', '.product-title', 'h1.a-size-large']
            for selector in title_selectors:
                title_elem = soup.select_one(selector)
                if title_elem:
                    product_info['title'] = title_elem.get_text().strip()
                    break
            
            # 提取价格
            price_selectors = ['.a-price .a-offscreen', '.a-price-whole', '.a-price-range']
            for selector in price_selectors:
                price_elem = soup.select_one(selector)
                if price_elem:
                    product_info['price'] = price_elem.get_text().strip()
                    break
            
            # 提取评分
            rating_elem = soup.select_one('.a-icon-alt')
            if rating_elem:
                rating_text = rating_elem.get_text().strip()
                if 'out of' in rating_text:
                    product_info['rating'] = rating_text
            
            # 提取评论数
            review_elem = soup.select_one('#acrCustomerReviewText')
            if review_elem:
                product_info['reviews'] = review_elem.get_text().strip()
            
            logger.info(f"产品解析成功: {product_info['title'][:50]}...")
            return product_info
            
        except Exception as e:
            logger.error(f"解析产品页面出错: {str(e)}")
            return None

def main():
    """主函数 - 测试Bing搜索Amazon功能"""
    print("=" * 60)
    print("🔍 Bing搜索Amazon采集工具")
    print("策略: 通过Bing搜索绕过Amazon反爬虫")
    print("关键词: fan")
    print("=" * 60)
    
    scraper = BingAmazonScraper()
    
    # 1. 测试代理
    if not scraper.test_proxy():
        print("❌ 代理测试失败")
        return
    
    # 2. 通过Bing搜索Amazon产品
    amazon_links = scraper.search_bing_for_amazon("fan", "美国", max_pages=2)
    
    if amazon_links:
        print(f"\n✅ 通过Bing找到 {len(amazon_links)} 个Amazon产品链接")
        print(f"✅ 提取到 {len(scraper.all_amazon_codes)} 个产品代码")
        
        # 显示前几个产品代码
        print("\n📋 产品代码列表:")
        for i, code in enumerate(scraper.all_amazon_codes[:5], 1):
            print(f"  {i}. {code}")
        
        # 3. 获取产品详细信息
        if scraper.all_amazon_codes:
            print(f"\n📦 获取产品详细信息...")
            
            for i, asin in enumerate(scraper.all_amazon_codes[:3], 1):  # 只测试前3个
                print(f"\n产品 {i}: {asin}")
                product_info = scraper.get_amazon_product_info(asin)
                
                if product_info:
                    print(f"  标题: {product_info['title'][:60]}...")
                    print(f"  价格: {product_info['price']}")
                    print(f"  评分: {product_info['rating']}")
                    print(f"  评论: {product_info['reviews']}")
                else:
                    print(f"  ❌ 获取产品信息失败")
        
        print("\n" + "=" * 60)
        print("🎉 Bing搜索策略成功!")
        print("✅ 成功绕过Amazon反爬虫")
        print("✅ 通过Bing获取产品链接")
        print("✅ 提取产品代码(ASIN)")
        print("✅ 获取产品详细信息")
        
        print("\n💡 优势:")
        print("- 不直接访问Amazon搜索页面")
        print("- 利用Bing搜索引擎的索引")
        print("- 绕过Amazon的反爬虫检测")
        print("- 获取真实的产品代码")
        
    else:
        print("\n❌ 未找到Amazon产品链接")
        print("可能原因:")
        print("- Bing搜索结果中没有相关产品")
        print("- 搜索查询需要优化")
        print("- 网络连接问题")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
