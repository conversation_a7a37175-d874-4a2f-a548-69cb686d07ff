2025-08-05 18:20:25,816 - INFO - Amazon HTTP采集器已初始化 - 国家: US, 域名: amazon.com
2025-08-05 18:20:25,816 - INFO - 使用123Proxy代理: proxy.123proxy.cn:36931
2025-08-05 18:20:25,817 - INFO - Amazon HTTP采集器已初始化 - 国家: US, 域名: amazon.com
2025-08-05 18:20:25,817 - INFO - 使用123Proxy代理: proxy.123proxy.cn:36931
2025-08-05 18:20:25,818 - INFO - Amazon HTTP采集器已初始化 - 国家: US, 域名: amazon.com
2025-08-05 18:20:25,818 - INFO - 使用123Proxy代理: proxy.123proxy.cn:36931
2025-08-05 18:20:25,818 - INFO - 测试代理连接...
2025-08-05 18:20:29,432 - INFO - ✅ 代理连接成功，当前IP: <!DOCTYPE html>
<html lang="en">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta http-equiv="content-style-type" content="text/css" />
    <meta http-equiv="content-script-type" content="text/javascript" />
    <meta http-equiv="content-language" content="en" />
    <meta http-equiv="pragma" content="no-cache" />
    <meta http-equiv="cache-control" content="no-cache" />
    <meta name="description" content="Get my IP Address" />
    <meta name="keywords" content="ip address ifconfig ifconfig.me" />
    <meta name="author" content="" />
    <link rel="shortcut icon" href="favicon.ico" />
    <link rel="canonical" href="https://ifconfig.me/" />
    <title>What Is My IP Address? - ifconfig.me</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="./static/styles/style.css" rel="stylesheet" type="text/css">
    <link href="https://fonts.googleapis.com/css?family=Open+Sans&display=swap" rel="stylesheet">
</head>

<body>
    <div id="ad_container">
        <div class="ad">
            Need a robust API to Geolocate IPs and fetch other crucial information? Try
            <a
                href="https://ipinfo.io/?utm_source=ifconfig.me&utm_medium=referral&utm_campaign=upsell_sister_sites">IPinfo.io</a>.
        </div>
    </div>

    <div id="container" class="clearfix">
        <div id="header">
            <table>
                <tr>
                    <td>
                        <h1><a href="http://ifconfig.me">What Is My IP Address? - ifconfig.me</a></h1>
                    </td>
                    <td></td>
                </tr>
                <tr>
                    <td></td>
                    <td>
                        <div id="plungins">
                            <div class="plungin" id="button_facebook">
                                <div id="fb-root"></div>
                                <script src="http://connect.facebook.net/en_US/all.js#xfbml=1"></script>
                                <fb:like href="http://ifconfig.me/" send="false" layout="button_count" width="100"
                                    show_faces="true" font=""></fb:like>
                            </div>

                            <div class="plungin" id="button_twitter">
                                <a href="http://twitter.com/share" class="twitter-share-button"
                                    data-url="http://ifconfig.me/" data-text="What Is My IP Address? - ifconfig.me"
                                    data-count="horizontal"></a>
                                <script type="text/javascript" src="http://platform.twitter.com/widgets.js"></script>
                            </div>

                            <div class="plungin" id="button_plusone">
                                <!-- Place this tag where you want the +1 button to render -->
                                <g:plusone size="medium" href="http://ifconfig.me/"></g:plusone>
                                <!-- Place this render call where appropriate -->
                                <script type="text/javascript">
                                    (function () {
                                        var po = document.createElement('script');
                                        po.type = 'text/javascript';
                                        po.async = true;
                                        po.src = 'https://apis.google.com/js/plusone.js';
                                        var s = document.getElementsByTagName('script')[0];
                                        s.parentNode.insertBefore(po, s);
                                    })();
                                </script>
                            </div>
                        </div>
                    </td>
                </tr>
            </table>
        </div>
        <div id="info_area">
            <h2>Your Connection</h2>
            <table id="info_table" summary="info">
                <tr>
                    <td class="info_table_label">IP Address</td>
                    <td id="ip_address_cell"><strong id="ip_address">
                            ***********
                        </strong></td>
                </tr>
                <tr>
                    <td class="info_table_label">User Agent</td>
                    <td>
                        Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/20100101 Firefox/121.0
                    </td>
                </tr>
                <tr>
                    <td class="info_table_label">Language</td>
                    <td>
                        en-GB,en;q=0.9
                    </td>
                </tr>
                <tr>
                    <td class="info_table_label">Referer</td>
                    <td>
                        
                    </td>
                </tr>
                <tr>
                    <td class="info_table_label">Method</td>
                    <td>
                        GET
                    </td>
                </tr>
                <tr>
                    <td class="info_table_label">Encoding</td>
                    <td>
                        gzip, deflate, br
                    </td>
                </tr>
                <tr>
                    <td class="info_table_label">MIME Type</td>
                    <td>
                        text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8
                    </td>
                </tr>
                <tr>
                    <td class="info_table_label">Charset</td>
                    <td>
                        
                    </td>
                </tr>
                <tr>
                    <td class="info_table_label">X-Forwarded-For</td>
                    <td>
                        ***********,**************
                    </td>
                </tr>
            </table>
        </div>
        <!--<div id="middle"></div>-->
        <div id="cli_wrap">
            <h2>Command Line Interface</h2>
            <table id="cli_table" summary="cli">
                <tr>
                    <td class="cli_command">$ curl ifconfig.me</td>
                    <td class="cli_arrow">&rArr;</td>
                    <td>
                        ***********
                    </td>
                </tr>
                <tr>
                    <td class="cli_command">$ curl ifconfig.me/ip</td>
                    <td class="cli_arrow">&rArr;</td>
                    <td>
                        ***********
                    </td>
                </tr>
                <tr>
                    <td class="cli_command">$ curl ifconfig.me/ua</td>
                    <td class="cli_arrow">&rArr;</td>
                    <td>
                        Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/20100101 Firefox/121.0
                    </td>
                </tr>
                <tr>
                    <td class="cli_command">$ curl ifconfig.me/lang</td>
                    <td class="cli_arrow">&rArr;</td>
                    <td>
                        en-GB,en;q=0.9
                    </td>
                </tr>
                <tr>
                    <td class="cli_command">$ curl ifconfig.me/encoding</td>
                    <td class="cli_arrow">&rArr;</td>
                    <td>
                        gzip, deflate, br
                    </td>
                </tr>
                <tr>
                    <td class="cli_command">$ curl ifconfig.me/mime</td>
                    <td class="cli_arrow">&rArr;</td>
                    <td>
                        text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8
                    </td>
                </tr>
                <tr>
                    <td class="cli_command">$ curl ifconfig.me/charset</td>
                    <td class="cli_arrow">&rArr;</td>
                    <td>
                        
                    </td>
                </tr>
                <tr>
                    <td class="cli_command">$ curl ifconfig.me/forwarded</td>
                    <td class="cli_arrow">&rArr;</td>
                    <td>
                        ***********,**************
                    </td>
                </tr>
                <tr>
                    <td class="cli_command">$ curl ifconfig.me/all</td>
                    <td class="cli_arrow">&rArr;</td>
                    <td>
                        
                            ip_addr: ***********
                            <br>
                        
                            remote_host: unavailable
                            <br>
                        
                            user_agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/20100101 Firefox/121.0
                            <br>
                        
                            port: 46560
                            <br>
                        
                            language: en-GB,en;q=0.9
                            <br>
                        
                            referer: 
                            <br>
                        
                            connection: 
                            <br>
                        
                            keep_alive: 
                            <br>
                        
                            method: GET
                            <br>
                        
                            encoding: gzip, deflate, br
                            <br>
                        
                            mime: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8
                            <br>
                        
                            charset: 
                            <br>
                        
                            via: 1.1 google
                            <br>
                        
                            forwarded: ***********,**************
                            <br>
                        
                            
                            <br>
                        
                    </td>
                </tr>
                <tr>
                    <td class="cli_command">$ curl ifconfig.me/all.json</td>
                    <td class="cli_arrow">&rArr;</td>
                    <td>
                        {&quot;ip_addr&quot;:&quot;***********&quot;,&quot;user_agent&quot;:&quot;Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/20100101 Firefox/121.0&quot;,&quot;port&quot;:&quot;46560&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;encoding&quot;:&quot;gzip, deflate, br&quot;,&quot;mime&quot;:&quot;text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8&quot;,&quot;via&quot;:&quot;1.1 google&quot;,&quot;forwarded&quot;:&quot;***********,**************&quot;,&quot;language&quot;:&quot;en-GB,en;q=0.9&quot;}
                    </td>
                </tr>

            </table>
        </div>
        <div id="footer">&copy; 2024 ifconfig.me</div>
    </div>
</body>

</html>
2025-08-05 18:21:12,128 - INFO - Amazon HTTP采集器已初始化 - 国家: US, 域名: amazon.com
2025-08-05 18:21:12,129 - INFO - 使用123Proxy代理: proxy.123proxy.cn:36931
2025-08-05 18:21:12,129 - INFO - Amazon HTTP采集器已初始化 - 国家: US, 域名: amazon.com
2025-08-05 18:21:12,129 - INFO - 使用123Proxy代理: proxy.123proxy.cn:36931
2025-08-05 18:21:12,130 - INFO - Amazon HTTP采集器已初始化 - 国家: US, 域名: amazon.com
2025-08-05 18:21:12,130 - INFO - 使用123Proxy代理: proxy.123proxy.cn:36931
2025-08-05 18:21:12,130 - INFO - 测试代理连接...
2025-08-05 18:21:15,585 - INFO - ✅ 代理连接成功，当前IP: ***************
2025-08-05 18:21:58,167 - INFO - Amazon HTTP采集器已初始化 - 国家: US, 域名: amazon.com
2025-08-05 18:21:58,168 - INFO - 使用123Proxy代理: proxy.123proxy.cn:36931
2025-08-05 18:21:58,168 - INFO - 测试代理连接...
2025-08-05 18:22:01,405 - INFO - ✅ 代理连接成功，当前IP: **************
2025-08-05 18:22:12,042 - INFO - 搜索关键词: fan
2025-08-05 18:22:12,042 - INFO - 正在搜索第 1 页...
2025-08-05 18:22:15,851 - WARNING - 服务不可用 (503)，尝试重试 (1/3)
2025-08-05 18:22:20,187 - INFO - Amazon HTTP采集器已初始化 - 国家: US, 域名: amazon.com
2025-08-05 18:22:20,188 - INFO - 使用123Proxy代理: proxy.123proxy.cn:36931
2025-08-05 18:22:20,188 - INFO - 测试代理连接...
2025-08-05 18:22:22,686 - INFO - ✅ 代理连接成功，当前IP: ************
2025-08-05 18:22:22,687 - INFO - 正在采集产品: https://www.amazon.com/dp/B08N5WRWNW
2025-08-05 18:22:26,395 - WARNING - 解压响应失败: Not a gzipped file (b'<!')
2025-08-05 18:22:26,395 - WARNING - 检测到反爬虫拦截，尝试重试 (1/3)
2025-08-05 18:22:34,540 - WARNING - 服务不可用 (503)，尝试重试 (2/3)
2025-08-05 18:22:36,518 - WARNING - 解压响应失败: Not a gzipped file (b'<!')
2025-08-05 18:22:36,519 - WARNING - 检测到反爬虫拦截，尝试重试 (2/3)
2025-08-05 18:22:45,437 - WARNING - 解压响应失败: Not a gzipped file (b'<!')
2025-08-05 18:22:45,437 - WARNING - 检测到反爬虫拦截，尝试重试 (3/3)
2025-08-05 18:22:47,627 - WARNING - 服务不可用 (503)，尝试重试 (3/3)
2025-08-05 18:22:52,633 - ERROR - 所有重试失败: https://www.amazon.com/dp/B08N5WRWNW
2025-08-05 18:22:52,633 - INFO - 搜索关键词: bluetooth headphones
2025-08-05 18:22:52,633 - INFO - 正在搜索第 1 页...
2025-08-05 18:22:54,349 - WARNING - 服务不可用 (503)，尝试重试 (1/3)
2025-08-05 18:27:11,529 - INFO - Amazon HTTP采集器已初始化 - 国家: US, 域名: amazon.com
2025-08-05 18:27:11,529 - INFO - 使用123Proxy代理: proxy.123proxy.cn:36931
2025-08-05 18:27:11,530 - INFO - 测试代理连接...
2025-08-05 18:27:13,619 - INFO - ✅ 代理连接成功，当前IP: 86.11.************-08-05 18:27:20,946 - WARNING - 解压响应失败: Not a gzipped file (b'<!')
2025-08-05 18:27:20,946 - WARNING - 检测到反爬虫指示器: captcha
2025-08-05 18:27:20,947 - WARNING - 检测到反爬虫拦截 (尝试 1/5)
2025-08-05 18:28:26,102 - WARNING - 解压响应失败: Not a gzipped file (b'<!')
2025-08-05 18:28:26,102 - WARNING - 检测到反爬虫指示器: captcha
2025-08-05 18:28:26,103 - WARNING - 检测到反爬虫拦截 (尝试 2/5)
2025-08-05 18:28:26,103 - INFO - 已清除会话cookies
2025-08-05 18:28:36,852 - WARNING - 解压响应失败: Not a gzipped file (b'<!')
2025-08-05 18:28:36,852 - WARNING - 检测到反爬虫指示器: captcha
2025-08-05 18:28:36,852 - WARNING - 检测到反爬虫拦截 (尝试 3/5)
2025-08-05 18:28:36,852 - INFO - 模拟访问: https://www.amazon.com
2025-08-05 18:28:55,573 - WARNING - 解压响应失败: Not a gzipped file (b'<!')
2025-08-05 18:28:55,573 - WARNING - 检测到反爬虫指示器: captcha
2025-08-05 18:28:55,574 - WARNING - 检测到反爬虫拦截 (尝试 4/5)
2025-08-05 18:30:48,741 - WARNING - 解压响应失败: Not a gzipped file (b'<!')
2025-08-05 18:30:48,741 - WARNING - 检测到反爬虫指示器: captcha
2025-08-05 18:30:48,741 - WARNING - 检测到反爬虫拦截 (尝试 5/5)
2025-08-05 18:30:48,741 - ERROR - 所有重试失败: https://www.amazon.com
2025-08-05 18:44:29,479 - INFO - Amazon HTTP采集器已初始化 - 国家: US, 域名: amazon.com
2025-08-05 18:44:29,480 - INFO - 使用123Proxy代理: proxy.123proxy.cn:36931
2025-08-05 18:44:29,480 - INFO - 测试代理连接...
2025-08-05 18:44:36,696 - INFO - ✅ 代理连接成功，当前IP: *************
2025-08-05 18:44:59,600 - INFO - 搜索关键词: fan
2025-08-05 18:44:59,600 - INFO - 正在搜索第 1 页...
2025-08-05 18:44:59,601 - INFO - 等待 3.4 秒后发送请求...
2025-08-05 18:45:14,842 - WARNING - 请求失败: HTTPSConnectionPool(host='www.amazon.com', port=443): Max retries exceeded with url: /s?k=fan&_t=1754390703&_r=4839 (Caused by ResponseError('too many 503 error responses')) (尝试 1/5)
2025-08-05 18:45:28,167 - INFO - 等待 19.8 秒后发送请求...
2025-08-05 18:45:55,817 - WARNING - 请求失败: HTTPSConnectionPool(host='www.amazon.com', port=443): Max retries exceeded with url: /s?k=fan&_t=1754390747&_r=5010 (Caused by ResponseError('too many 503 error responses')) (尝试 2/5)
2025-08-05 18:46:01,593 - INFO - 等待 21.5 秒后发送请求...
