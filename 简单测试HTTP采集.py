#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试HTTP采集功能
"""

import requests
import time
import random
from bs4 import BeautifulSoup

# 123Proxy代理配置
http_proxy = "http://u1856561711670614:<EMAIL>:36931"
https_proxy = "http://u1856561711670614:<EMAIL>:36931"

proxySettings = {
    "http": http_proxy,
    "https": https_proxy,
}

def test_http_collection():
    """测试HTTP采集功能"""
    print("=" * 60)
    print("HTTP采集功能测试")
    print("=" * 60)
    
    # 创建session
    session = requests.Session()
    session.proxies = proxySettings
    
    # 设置请求头
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    }
    session.headers.update(headers)
    
    # 1. 测试代理IP
    print("1. 测试代理连接...")
    try:
        response = session.get("https://ifconfig.me", timeout=10)
        if response.status_code == 200:
            ip = response.text.strip()
            print(f"✅ 代理IP: {ip}")
        else:
            print(f"❌ 代理测试失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 代理连接错误: {str(e)}")
        return False
    
    # 2. 测试简单网站采集
    print("\n2. 测试简单网站采集...")
    test_sites = [
        "https://httpbin.org/json",
        "https://jsonplaceholder.typicode.com/posts/1",
        "https://httpbin.org/user-agent"
    ]
    
    for site in test_sites:
        try:
            print(f"正在采集: {site}")
            response = session.get(site, timeout=10)
            
            if response.status_code == 200:
                print(f"✅ 采集成功 (状态码: {response.status_code})")
                
                # 尝试解析JSON
                try:
                    data = response.json()
                    print(f"   JSON数据: {str(data)[:100]}...")
                except:
                    print(f"   文本数据: {response.text[:100]}...")
            else:
                print(f"❌ 采集失败 (状态码: {response.status_code})")
                
            time.sleep(1)  # 延迟
            
        except Exception as e:
            print(f"❌ 采集错误: {str(e)}")
    
    # 3. 测试HTML解析
    print("\n3. 测试HTML解析...")
    try:
        print("正在采集示例网站...")
        response = session.get("https://httpbin.org/html", timeout=10)
        
        if response.status_code == 200:
            print(f"✅ HTML采集成功 (状态码: {response.status_code})")
            
            # 解析HTML
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # 提取标题
            title = soup.find('title')
            if title:
                print(f"   页面标题: {title.get_text()}")
            
            # 提取所有链接
            links = soup.find_all('a')
            print(f"   找到 {len(links)} 个链接")
            
            # 提取所有段落
            paragraphs = soup.find_all('p')
            print(f"   找到 {len(paragraphs)} 个段落")
            
        else:
            print(f"❌ HTML采集失败 (状态码: {response.status_code})")
            
    except Exception as e:
        print(f"❌ HTML解析错误: {str(e)}")
    
    # 4. 测试多次请求验证IP轮换
    print("\n4. 测试IP轮换...")
    ips = []
    for i in range(3):
        try:
            print(f"第 {i+1} 次请求...")
            response = session.get("https://ifconfig.me", timeout=10)
            
            if response.status_code == 200:
                ip = response.text.strip()
                ips.append(ip)
                print(f"   IP: {ip}")
            
            time.sleep(2)  # 等待2秒
            
        except Exception as e:
            print(f"   错误: {str(e)}")
    
    unique_ips = set(ips)
    print(f"\n获取到 {len(unique_ips)} 个不同IP:")
    for ip in unique_ips:
        print(f"  - {ip}")
    
    return True

def test_data_extraction():
    """测试数据提取功能"""
    print("\n" + "=" * 60)
    print("数据提取功能测试")
    print("=" * 60)
    
    session = requests.Session()
    session.proxies = proxySettings
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    })
    
    try:
        # 测试JSON API
        print("1. 测试JSON API数据提取...")
        response = session.get("https://jsonplaceholder.typicode.com/posts", timeout=10)
        
        if response.status_code == 200:
            posts = response.json()
            print(f"✅ 获取到 {len(posts)} 条数据")
            
            # 提取前3条数据
            for i, post in enumerate(posts[:3], 1):
                print(f"数据 {i}:")
                print(f"  ID: {post.get('id')}")
                print(f"  标题: {post.get('title', '')[:50]}...")
                print(f"  内容: {post.get('body', '')[:50]}...")
        
        # 测试用户信息API
        print("\n2. 测试用户信息API...")
        response = session.get("https://jsonplaceholder.typicode.com/users", timeout=10)
        
        if response.status_code == 200:
            users = response.json()
            print(f"✅ 获取到 {len(users)} 个用户")
            
            # 提取前3个用户
            for i, user in enumerate(users[:3], 1):
                print(f"用户 {i}:")
                print(f"  姓名: {user.get('name')}")
                print(f"  邮箱: {user.get('email')}")
                print(f"  网站: {user.get('website')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据提取测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("HTTP采集功能测试工具")
    print("测试使用123Proxy代理进行HTTP数据采集\n")
    
    # 基础HTTP采集测试
    success1 = test_http_collection()
    
    if success1:
        # 数据提取测试
        success2 = test_data_extraction()
        
        if success1 and success2:
            print("\n" + "=" * 60)
            print("✅ HTTP采集功能测试全部通过!")
            print("采集_HTTP版.py 已准备就绪")
            print("可以用于:")
            print("- 网页内容采集")
            print("- API数据获取") 
            print("- HTML解析")
            print("- JSON数据处理")
            print("- 通过123Proxy代理访问")
            print("=" * 60)
        else:
            print("\n❌ 部分测试失败")
    else:
        print("\n❌ 基础测试失败，请检查代理配置")

if __name__ == "__main__":
    main()
