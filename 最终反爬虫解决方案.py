#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终反爬虫解决方案 - 使用多种策略绕过Amazon反爬虫
包含完整的HTTP采集工具优化版本
"""

import requests
import time
import random
from bs4 import BeautifulSoup
import re
import json

# 123Proxy代理配置
http_proxy = "http://u1856561711670614:<EMAIL>:36931"
https_proxy = "http://u1856561711670614:<EMAIL>:36931"

proxySettings = {
    "http": http_proxy,
    "https": https_proxy,
}

class AdvancedAmazonScraper:
    """高级Amazon反爬虫采集器"""
    
    def __init__(self):
        self.session = None
        self.request_count = 0
        self.last_request_time = 0
        self.setup_session()
    
    def setup_session(self):
        """设置高级会话"""
        self.session = requests.Session()
        self.session.proxies = proxySettings
        
        # 设置高级请求头
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0',
            'DNT': '1'
        }
        
        self.session.headers.update(headers)
        
        # 设置会话cookies
        self.session.cookies.set('session-id', f"{random.randint(100,999)}-{random.randint(1000000,9999999)}-{random.randint(1000000,9999999)}")
        self.session.cookies.set('ubid-main', f"{''.join(random.choices('0123456789ABCDEF', k=10))}")
    
    def smart_delay(self):
        """智能延迟策略"""
        current_time = time.time()
        
        # 确保请求间隔至少5秒
        if self.last_request_time > 0:
            elapsed = current_time - self.last_request_time
            if elapsed < 5:
                wait_time = 5 - elapsed + random.uniform(2, 8)
                print(f"    ⏱️ 智能延迟 {wait_time:.1f} 秒...")
                time.sleep(wait_time)
        
        self.last_request_time = time.time()
        self.request_count += 1
        
        # 每10个请求后长时间休息
        if self.request_count % 10 == 0:
            rest_time = random.uniform(30, 60)
            print(f"    😴 长时间休息 {rest_time:.1f} 秒...")
            time.sleep(rest_time)
    
    def test_proxy(self):
        """测试代理连接"""
        try:
            response = self.session.get("https://ifconfig.me/ip", timeout=15)
            if response.status_code == 200:
                ip = response.text.strip()
                print(f"✅ 代理连接成功，IP: {ip}")
                return True
            return False
        except Exception as e:
            print(f"❌ 代理连接失败: {str(e)}")
            return False
    
    def warm_up_session(self):
        """预热会话 - 访问一些安全页面建立信任"""
        print("🔥 预热会话...")
        
        safe_pages = [
            "https://www.amazon.com/gp/help/customer/display.html?nodeId=508510",  # 帮助页面
            "https://www.amazon.com/gp/site-directory",                           # 站点目录
            "https://www.amazon.com/gp/bestsellers",                             # 畅销榜
        ]
        
        for i, url in enumerate(safe_pages, 1):
            try:
                print(f"  {i}. 访问: {url.split('/')[-1]}")
                self.smart_delay()
                
                response = self.session.get(url, timeout=20)
                if response.status_code == 200:
                    print(f"     ✅ 成功 ({len(response.text)} 字符)")
                else:
                    print(f"     ⚠️ 状态码: {response.status_code}")
                    
            except Exception as e:
                print(f"     ❌ 错误: {str(e)}")
        
        print("🔥 会话预热完成")
        return True
    
    def search_alternative_methods(self, keyword="fan"):
        """使用多种方法尝试搜索"""
        print(f"\n🎯 多方法搜索: {keyword}")
        
        methods = [
            self.method_1_direct_search,
            self.method_2_category_search,
            self.method_3_suggestion_search,
        ]
        
        for i, method in enumerate(methods, 1):
            print(f"\n  方法 {i}: {method.__name__}")
            try:
                result = method(keyword)
                if result:
                    print(f"  ✅ 方法 {i} 成功!")
                    return result
                else:
                    print(f"  ❌ 方法 {i} 失败")
            except Exception as e:
                print(f"  ❌ 方法 {i} 错误: {str(e)}")
        
        return None
    
    def method_1_direct_search(self, keyword):
        """方法1: 直接搜索"""
        self.smart_delay()
        
        # 先访问首页建立referer
        self.session.get("https://www.amazon.com", timeout=20)
        time.sleep(random.uniform(2, 4))
        
        # 设置referer
        self.session.headers.update({'Referer': 'https://www.amazon.com/'})
        
        search_url = f"https://www.amazon.com/s?k={keyword}&ref=nb_sb_noss_2"
        response = self.session.get(search_url, timeout=30)
        
        print(f"    状态码: {response.status_code}, 大小: {len(response.text)}")
        
        if response.status_code == 200:
            return self.parse_search_results(response.text, keyword)
        return None
    
    def method_2_category_search(self, keyword):
        """方法2: 通过分类搜索"""
        self.smart_delay()
        
        # 先访问家居分类 (风扇通常在这里)
        category_url = "https://www.amazon.com/s?k=fan&i=garden&ref=sr_pg_1"
        
        self.session.headers.update({'Referer': 'https://www.amazon.com/gp/bestsellers'})
        
        response = self.session.get(category_url, timeout=30)
        
        print(f"    状态码: {response.status_code}, 大小: {len(response.text)}")
        
        if response.status_code == 200:
            return self.parse_search_results(response.text, keyword)
        return None
    
    def method_3_suggestion_search(self, keyword):
        """方法3: 通过搜索建议"""
        self.smart_delay()
        
        # 使用搜索建议API的URL格式
        suggest_url = f"https://www.amazon.com/s?k={keyword}&i=aps&ref=sr_pg_1"
        
        self.session.headers.update({'Referer': 'https://www.amazon.com/gp/site-directory'})
        
        response = self.session.get(suggest_url, timeout=30)
        
        print(f"    状态码: {response.status_code}, 大小: {len(response.text)}")
        
        if response.status_code == 200:
            return self.parse_search_results(response.text, keyword)
        return None
    
    def parse_search_results(self, content, keyword):
        """解析搜索结果"""
        # 检查反爬虫
        if self.is_blocked(content):
            print("    ❌ 检测到反爬虫拦截")
            return None
        
        try:
            soup = BeautifulSoup(content, 'html.parser')
            
            # 多种产品选择器
            selectors = [
                'div[data-component-type="s-search-result"]',
                'div.s-result-item',
                'div[data-asin]',
                '.sg-col-inner'
            ]
            
            products = []
            for selector in selectors:
                products = soup.select(selector)
                if products:
                    break
            
            print(f"    📦 找到 {len(products)} 个产品")
            
            if len(products) > 0:
                results = []
                for i, product in enumerate(products[:5]):
                    try:
                        # 提取产品信息
                        product_info = self.extract_product_info(product)
                        if product_info:
                            results.append(product_info)
                            print(f"      {i+1}. {product_info['title'][:50]}...")
                            if product_info.get('price'):
                                print(f"         💰 {product_info['price']}")
                    except:
                        continue
                
                return results if results else None
            
        except Exception as e:
            print(f"    ❌ 解析错误: {str(e)}")
        
        return None
    
    def extract_product_info(self, product_elem):
        """提取产品信息"""
        info = {}
        
        try:
            # 标题
            title_elem = product_elem.find('h2') or product_elem.find('a', class_='s-link-style')
            if title_elem:
                title_link = title_elem.find('a') if title_elem.name != 'a' else title_elem
                if title_link:
                    info['title'] = title_link.get_text().strip()
                    info['url'] = title_link.get('href', '')
            
            # 价格
            price_elem = product_elem.find('span', class_='a-price-whole') or product_elem.find('span', class_='a-price')
            if price_elem:
                info['price'] = price_elem.get_text().strip()
            
            # ASIN
            asin = product_elem.get('data-asin')
            if asin:
                info['asin'] = asin
            
            return info if info.get('title') else None
            
        except:
            return None
    
    def is_blocked(self, content):
        """检查是否被拦截"""
        if not content or len(content) < 1000:
            return True
        
        blocked_indicators = [
            'robot check', 'captcha', 'blocked', 'unusual traffic',
            'verify you are human', 'security check', 'access denied'
        ]
        
        content_lower = content.lower()
        for indicator in blocked_indicators:
            if indicator in content_lower:
                print(f"    ⚠️ 检测到: {indicator}")
                return True
        
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 最终反爬虫解决方案")
    print("关键词: fan")
    print("策略: 多方法 + 智能延迟 + 会话预热")
    print("=" * 60)
    
    # 创建采集器
    scraper = AdvancedAmazonScraper()
    
    # 1. 测试代理
    print("\n1. 测试代理连接...")
    if not scraper.test_proxy():
        print("❌ 代理测试失败")
        return
    
    # 2. 预热会话
    print("\n2. 预热会话...")
    scraper.warm_up_session()
    
    # 3. 多方法搜索
    print("\n3. 开始搜索...")
    results = scraper.search_alternative_methods("fan")
    
    # 4. 结果总结
    print("\n" + "=" * 60)
    if results:
        print("🎉 反爬虫绕过成功!")
        print(f"✅ 成功获取 {len(results)} 个产品")
        print("✅ 代理连接稳定")
        print("✅ 会话预热有效")
        print("✅ 多方法策略成功")
        
        print("\n📋 获取的产品:")
        for i, product in enumerate(results, 1):
            print(f"  {i}. {product['title'][:60]}...")
            if product.get('price'):
                print(f"     💰 价格: {product['price']}")
            if product.get('asin'):
                print(f"     🔖 ASIN: {product['asin']}")
        
        print("\n🛡️ 成功要素:")
        print("- 123Proxy代理稳定")
        print("- 智能请求延迟")
        print("- 会话预热建立信任")
        print("- 多种搜索方法")
        print("- 真实浏览器头部")
        print("- 合理的Referer设置")
        
    else:
        print("⚠️ 部分成功")
        print("✅ 代理连接正常")
        print("✅ 基础页面访问正常")
        print("❌ 搜索功能仍受限")
        
        print("\n🔧 建议:")
        print("- Amazon搜索页面保护较强")
        print("- 可以尝试采集具体产品页面")
        print("- 考虑使用浏览器自动化")
        print("- 或通过分类页面获取产品")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
