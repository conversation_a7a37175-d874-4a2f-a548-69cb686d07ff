#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试完整版HTTP采集工具
"""

import sys
import os

def test_import():
    """测试导入"""
    try:
        print("正在导入采集_HTTP版模块...")
        from 采集_HTTP版 import AmazonHTTPScraper, AMAZON_DOMAINS
        print("✅ 模块导入成功")
        return True
    except Exception as e:
        print(f"❌ 模块导入失败: {str(e)}")
        return False

def test_initialization():
    """测试初始化"""
    try:
        print("正在初始化采集器...")
        from 采集_HTTP版 import AmazonHTTPScraper
        
        scraper = AmazonHTTPScraper(country='US', max_workers=2)
        print("✅ 采集器初始化成功")
        print(f"域名: {scraper.domain}")
        print(f"基础URL: {scraper.base_url}")
        return scraper
    except Exception as e:
        print(f"❌ 采集器初始化失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def test_headers():
    """测试请求头生成"""
    try:
        print("正在测试请求头生成...")
        from 采集_HTTP版 import AmazonHTTPScraper
        
        scraper = AmazonHTTPScraper(country='US', max_workers=1)
        headers = scraper.get_headers()
        
        print("✅ 请求头生成成功")
        print(f"User-Agent: {headers.get('User-Agent', 'N/A')[:50]}...")
        print(f"Accept: {headers.get('Accept', 'N/A')[:50]}...")
        return True
    except Exception as e:
        print(f"❌ 请求头生成失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_proxy():
    """测试代理连接"""
    try:
        print("正在测试代理连接...")
        from 采集_HTTP版 import AmazonHTTPScraper
        
        scraper = AmazonHTTPScraper(country='US', max_workers=1)
        
        if scraper.test_proxy_connection():
            print("✅ 代理连接测试成功")
            return True
        else:
            print("❌ 代理连接测试失败")
            return False
    except Exception as e:
        print(f"❌ 代理连接测试出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("简单测试完整版HTTP采集工具")
    print("=" * 50)
    
    # 1. 测试导入
    if not test_import():
        return
    
    # 2. 测试初始化
    scraper = test_initialization()
    if not scraper:
        return
    
    # 3. 测试请求头
    if not test_headers():
        return
    
    # 4. 测试代理
    if not test_proxy():
        return
    
    print("\n" + "=" * 50)
    print("✅ 所有基础测试通过!")
    print("完整版HTTP采集工具准备就绪")
    print("=" * 50)

if __name__ == "__main__":
    main()
