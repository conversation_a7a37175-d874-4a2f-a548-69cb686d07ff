#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试专利.py的代理配置
"""

import requests
import time

# 从专利.py复制的代理配置
http_proxy = "http://u1856561711670614:<EMAIL>:36931"
https_proxy = "http://u1856561711670614:<EMAIL>:36931"

proxySettings = {
    "http": http_proxy,
    "https": https_proxy,
}

def test_proxy():
    """测试代理连接"""
    print("测试专利.py的123Proxy代理配置...")
    print(f"代理服务器: proxy.123proxy.cn:36931")
    print(f"用户名: u1856561711670614")
    print("-" * 50)
    
    try:
        print("正在连接代理服务器...")
        session = requests.Session()
        
        # 测试连接
        response = session.get("https://ifconfig.me", proxies=proxySettings, timeout=15)
        
        if response.status_code == 200:
            ip = response.text.strip()
            print(f"✅ 代理连接成功!")
            print(f"当前IP地址: {ip}")
            
            # 再测试一次验证IP轮换
            print("\n等待3秒后再次测试...")
            time.sleep(3)
            
            response2 = session.get("https://ifconfig.me", proxies=proxySettings, timeout=15)
            if response2.status_code == 200:
                ip2 = response2.text.strip()
                print(f"第二次请求IP: {ip2}")
                
                if ip != ip2:
                    print("✅ IP地址发生变化，代理轮换正常")
                else:
                    print("ℹ️ IP地址相同，可能是正常情况")
            
            return True
            
        else:
            print(f"❌ 请求失败，HTTP状态码: {response.status_code}")
            return False
            
    except requests.exceptions.ProxyError as e:
        print(f"❌ 代理连接错误: {str(e)}")
        print("可能的原因:")
        print("1. 代理服务器地址或端口错误")
        print("2. 用户名或密码错误")
        print("3. 123Proxy账户流量不足")
        return False
        
    except requests.exceptions.Timeout as e:
        print(f"❌ 连接超时: {str(e)}")
        print("可能的原因:")
        print("1. 网络连接不稳定")
        print("2. 代理服务器响应慢")
        return False
        
    except requests.exceptions.ConnectionError as e:
        print(f"❌ 连接错误: {str(e)}")
        print("可能的原因:")
        print("1. 网络连接问题")
        print("2. 代理服务器不可用")
        return False
        
    except Exception as e:
        print(f"❌ 其他错误: {str(e)}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("专利.py 123Proxy代理配置测试")
    print("=" * 60)
    
    success = test_proxy()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ 专利.py 代理配置测试成功!")
        print("现在专利.py文件已经配置好123Proxy代理")
        print("所有HTTP请求都会通过代理服务器")
    else:
        print("❌ 专利.py 代理配置测试失败!")
        print("请检查网络连接和123Proxy账户状态")
    print("=" * 60)
