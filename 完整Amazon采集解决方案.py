#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整Amazon采集解决方案
策略：Bing搜索 -> Amazon产品信息采集
基于真实浏览器行为模式的HTTP采集器
"""

import requests
import time
import random
import re
from bs4 import BeautifulSoup
from urllib.parse import urlparse, parse_qs, unquote
import logging
import json
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 123Proxy代理配置
PROXY_CONFIG = {
    "http": "http://u1856561711670614:<EMAIL>:36931",
    "https": "http://u1856561711670614:<EMAIL>:36931",
}

class AmazonProductScraper:
    """Amazon产品采集器 - 通过Bing搜索入口"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.proxies = PROXY_CONFIG
        
        # 基于真实浏览器的请求头
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0',
            'DNT': '1'
        }
        
        self.session.headers.update(self.headers)
        
        # 国家配置
        self.countries = {
            "美国": {"domain": "amazon.com", "currency": "USD"},
            "加拿大": {"domain": "amazon.ca", "currency": "CAD"},
            "日本": {"domain": "amazon.co.jp", "currency": "JPY"}
        }
        
        # 结果存储
        self.products = []
        
        logger.info("Amazon产品采集器初始化完成")
    
    def test_connection(self):
        """测试代理连接"""
        try:
            response = self.session.get("https://ifconfig.me/ip", timeout=15)
            if response.status_code == 200:
                ip = response.text.strip()
                logger.info(f"✅ 代理连接成功，IP: {ip}")
                return True
            return False
        except Exception as e:
            logger.error(f"❌ 代理连接失败: {str(e)}")
            return False
    
    def search_bing_for_amazon(self, keyword, country="美国", max_pages=2):
        """通过Bing搜索Amazon产品"""
        logger.info(f"🔍 开始Bing搜索: {keyword} (国家: {country})")
        
        if country not in self.countries:
            logger.error(f"不支持的国家: {country}")
            return []
        
        domain = self.countries[country]["domain"]
        search_query = f"site:{domain} {keyword} -maps -images"
        
        amazon_links = []
        
        for page in range(max_pages):
            logger.info(f"搜索第 {page + 1} 页...")
            
            # 构建Bing搜索URL
            if page == 0:
                search_url = f"https://www.bing.com/search?q={requests.utils.quote(search_query)}&setmkt=en-US&setlang=en"
            else:
                first_param = page * 10
                search_url = f"https://www.bing.com/search?q={requests.utils.quote(search_query)}&first={first_param}&setmkt=en-US&setlang=en"
            
            try:
                # 随机延迟
                time.sleep(random.uniform(2, 4))
                
                response = self.session.get(search_url, timeout=30)
                
                if response.status_code == 200:
                    logger.info(f"✅ Bing搜索成功，页面大小: {len(response.text)}")
                    
                    # 解析搜索结果
                    page_links = self.parse_bing_results(response.text, domain)
                    amazon_links.extend(page_links)
                    
                    logger.info(f"第 {page + 1} 页找到 {len(page_links)} 个Amazon链接")
                    
                else:
                    logger.warning(f"Bing搜索失败，状态码: {response.status_code}")
                    break
                    
            except Exception as e:
                logger.error(f"Bing搜索出错: {str(e)}")
                break
        
        logger.info(f"总共找到 {len(amazon_links)} 个Amazon链接")
        return amazon_links
    
    def parse_bing_results(self, html_content, target_domain):
        """解析Bing搜索结果"""
        amazon_links = []
        
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Bing搜索结果选择器
            selectors = [
                'li.b_algo h2 a',
                'li.b_algo a',
                '.b_title a',
                'a[href*="amazon"]'
            ]
            
            for selector in selectors:
                links = soup.select(selector)
                
                for link in links:
                    href = link.get('href', '')
                    if href and target_domain in href and 'maps?' not in href:
                        # 清理和验证链接
                        clean_link = self.clean_amazon_link(href)
                        if clean_link and clean_link not in amazon_links:
                            amazon_links.append(clean_link)
                            logger.info(f"找到Amazon链接: {clean_link}")
        
        except Exception as e:
            logger.error(f"解析Bing结果出错: {str(e)}")
        
        return amazon_links
    
    def clean_amazon_link(self, url):
        """清理Amazon链接"""
        try:
            # 处理Bing重定向
            if 'bing.com/ck' in url:
                parsed = urlparse(url)
                query = parse_qs(parsed.query)
                encoded_url = query.get('u', [None])[0]
                if encoded_url:
                    try:
                        import base64
                        decoded_url = base64.b64decode(encoded_url + '==').decode('utf-8')
                        return decoded_url
                    except:
                        return unquote(encoded_url)
            
            # URL解码
            if '%' in url:
                url = unquote(url)
            
            # 验证是否为有效的Amazon链接
            if 'amazon' in url and ('s?' in url or '/dp/' in url or '/gp/product/' in url):
                return url
                
        except Exception as e:
            logger.debug(f"清理链接出错: {str(e)}")
        
        return None
    
    def scrape_amazon_search_page(self, search_url):
        """采集Amazon搜索页面"""
        logger.info(f"📄 采集Amazon搜索页面: {search_url}")
        
        try:
            # 设置来源为Bing
            headers = self.headers.copy()
            headers['Referer'] = 'https://www.bing.com/'
            
            # 随机延迟
            time.sleep(random.uniform(3, 6))
            
            response = self.session.get(search_url, headers=headers, timeout=30)
            
            if response.status_code == 200:
                logger.info(f"✅ Amazon搜索页面访问成功，大小: {len(response.text)}")
                
                # 检查反爬虫
                if self.check_anti_bot(response.text):
                    logger.warning("⚠️ 检测到反爬虫拦截")
                    return []
                
                # 解析产品
                products = self.parse_amazon_search_page(response.text)
                logger.info(f"✅ 解析到 {len(products)} 个产品")
                
                return products
                
            else:
                logger.warning(f"Amazon搜索页面访问失败: {response.status_code}")
                return []
                
        except Exception as e:
            logger.error(f"采集Amazon搜索页面出错: {str(e)}")
            return []
    
    def check_anti_bot(self, html_content):
        """检查反爬虫拦截"""
        anti_bot_keywords = [
            'captcha', 'robot check', 'blocked', 
            'unusual traffic', 'verify you are human',
            'sorry, something went wrong'
        ]
        
        content_lower = html_content.lower()
        return any(keyword in content_lower for keyword in anti_bot_keywords)
    
    def parse_amazon_search_page(self, html_content):
        """解析Amazon搜索页面"""
        products = []
        
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 查找产品容器
            product_containers = soup.select('div[data-component-type="s-search-result"]')
            
            if not product_containers:
                # 备用选择器
                product_containers = soup.select('div[data-asin]')
            
            logger.info(f"找到 {len(product_containers)} 个产品容器")
            
            for container in product_containers:
                try:
                    product = self.extract_product_from_container(container)
                    if product:
                        products.append(product)
                except Exception as e:
                    logger.debug(f"解析单个产品出错: {str(e)}")
                    continue
        
        except Exception as e:
            logger.error(f"解析Amazon搜索页面出错: {str(e)}")
        
        return products
    
    def extract_product_from_container(self, container):
        """从产品容器中提取信息"""
        product = {
            'asin': '',
            'title': '',
            'price': '',
            'rating': '',
            'reviews': '',
            'image_url': '',
            'product_url': '',
            'scraped_at': datetime.now().isoformat()
        }
        
        try:
            # 提取ASIN
            asin = container.get('data-asin')
            if asin:
                product['asin'] = asin
            
            # 提取标题和链接
            title_link = container.select_one('h2 a, .s-link-style')
            if title_link:
                product['title'] = title_link.get_text().strip()
                href = title_link.get('href', '')
                if href:
                    if href.startswith('/'):
                        product['product_url'] = f"https://www.amazon.com{href}"
                    else:
                        product['product_url'] = href
            
            # 提取价格
            price_selectors = [
                '.a-price .a-offscreen',
                '.a-price-whole',
                '.a-price'
            ]
            
            for selector in price_selectors:
                price_elem = container.select_one(selector)
                if price_elem:
                    product['price'] = price_elem.get_text().strip()
                    break
            
            # 提取评分
            rating_elem = container.select_one('.a-icon-alt')
            if rating_elem:
                rating_text = rating_elem.get_text().strip()
                if 'out of' in rating_text or '颗星' in rating_text:
                    product['rating'] = rating_text
            
            # 提取评论数
            review_elem = container.select_one('a[href*="#customerReviews"]')
            if review_elem:
                product['reviews'] = review_elem.get_text().strip()
            
            # 提取图片
            img_elem = container.select_one('img')
            if img_elem:
                product['image_url'] = img_elem.get('src', '')
            
            # 验证产品信息完整性
            if product['asin'] or product['title']:
                return product
                
        except Exception as e:
            logger.debug(f"提取产品信息出错: {str(e)}")
        
        return None
    
    def save_results(self, filename=None):
        """保存采集结果"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"amazon_products_{timestamp}.json"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.products, f, ensure_ascii=False, indent=2)
            
            logger.info(f"✅ 结果已保存到: {filename}")
            return filename
            
        except Exception as e:
            logger.error(f"保存结果失败: {str(e)}")
            return None
    
    def run_full_scraping(self, keyword, country="美国", max_bing_pages=2):
        """运行完整采集流程"""
        logger.info(f"🚀 开始完整采集流程: {keyword}")
        
        # 1. 测试连接
        if not self.test_connection():
            return False
        
        # 2. Bing搜索获取Amazon链接
        amazon_links = self.search_bing_for_amazon(keyword, country, max_bing_pages)
        
        if not amazon_links:
            logger.warning("未找到Amazon链接")
            return False
        
        # 3. 采集Amazon页面
        all_products = []
        
        for i, link in enumerate(amazon_links[:5], 1):  # 限制处理前5个链接
            logger.info(f"处理链接 {i}/{min(5, len(amazon_links))}: {link}")
            
            if '/s?' in link:  # 搜索页面
                products = self.scrape_amazon_search_page(link)
                all_products.extend(products)
            elif '/dp/' in link:  # 产品页面
                # 这里可以添加单个产品页面的处理逻辑
                logger.info(f"发现产品页面: {link}")
            
            # 延迟
            time.sleep(random.uniform(5, 8))
        
        # 4. 保存结果
        self.products = all_products
        
        if self.products:
            filename = self.save_results()
            logger.info(f"🎉 采集完成！共获取 {len(self.products)} 个产品")
            return True
        else:
            logger.warning("未获取到任何产品信息")
            return False

def main():
    """主函数"""
    print("=" * 80)
    print("🛒 Amazon产品采集器")
    print("策略: Bing搜索 -> Amazon产品信息采集")
    print("基于真实浏览器行为模式")
    print("=" * 80)
    
    scraper = AmazonProductScraper()
    
    # 运行采集
    success = scraper.run_full_scraping("fan", "美国", 2)
    
    if success:
        print("\n" + "=" * 80)
        print("🎉 采集任务完成！")
        print("✅ 成功绕过Amazon反爬虫")
        print("✅ 通过Bing搜索获取产品链接")
        print("✅ 提取产品详细信息")
        print("✅ 保存结构化数据")
        
        # 显示部分结果
        if scraper.products:
            print(f"\n📊 采集结果预览 (共{len(scraper.products)}个产品):")
            for i, product in enumerate(scraper.products[:3], 1):
                print(f"\n产品 {i}:")
                print(f"  ASIN: {product.get('asin', 'N/A')}")
                print(f"  标题: {product.get('title', 'N/A')[:60]}...")
                print(f"  价格: {product.get('price', 'N/A')}")
                print(f"  评分: {product.get('rating', 'N/A')}")
        
        print("\n💡 关键成功要素:")
        print("- Bing搜索作为入口绕过直接访问限制")
        print("- 真实浏览器请求头模拟")
        print("- 123Proxy代理稳定支持")
        print("- 合理的延迟和重试策略")
        
    else:
        print("\n⚠️ 采集任务未完全成功")
        print("请检查网络连接和代理设置")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
