#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试采集.py的配置加载功能
"""

import json
import os
import sys

def test_config_loading():
    """测试配置加载功能"""
    print("=== 测试配置加载功能 ===")
    
    # 检查配置文件是否存在
    config_file = "amazon_countries_config.json"
    if os.path.exists(config_file):
        print(f"✅ 配置文件存在: {config_file}")
        
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            countries = config_data.get('countries', {})
            proxy_config = config_data.get('proxy_config', {})
            
            print(f"✅ 配置文件解析成功")
            print(f"   - 国家数量: {len(countries)}")
            print(f"   - 代理用户名: {proxy_config.get('username', 'N/A')}")
            print(f"   - 代理服务器数量: {len(proxy_config.get('servers', []))}")
            
            print("\n📋 可用国家列表:")
            for country, config in countries.items():
                print(f"   - {country}: {config['domain']} ({config['lang']})")
            
            print("\n🔗 代理配置:")
            for server in proxy_config.get('servers', []):
                print(f"   - {server['host']}:{server['port']}")
                
        except Exception as e:
            print(f"❌ 配置文件解析失败: {str(e)}")
            return False
    else:
        print(f"❌ 配置文件不存在: {config_file}")
        return False
    
    return True

def test_import_scraper():
    """测试导入采集模块"""
    print("\n=== 测试导入采集模块 ===")
    
    try:
        # 尝试导入采集模块的关键部分
        import tkinter as tk
        print("✅ tkinter 导入成功")
        
        import requests
        print("✅ requests 导入成功")
        
        import pandas as pd
        print("✅ pandas 导入成功")
        
        # 测试创建一个简单的会话
        session = requests.Session()
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        print("✅ requests.Session 创建成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试采集.py的配置系统")
    print("=" * 50)
    
    # 测试配置加载
    config_ok = test_config_loading()
    
    # 测试模块导入
    import_ok = test_import_scraper()
    
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    print(f"   配置加载: {'✅ 通过' if config_ok else '❌ 失败'}")
    print(f"   模块导入: {'✅ 通过' if import_ok else '❌ 失败'}")
    
    if config_ok and import_ok:
        print("\n🎉 所有测试通过！采集.py的配置系统已准备就绪。")
        print("\n💡 使用说明:")
        print("   1. 运行 python 采集.py 启动程序")
        print("   2. 点击'测试代理连接'按钮测试代理")
        print("   3. 点击'刷新配置'按钮重新加载配置")
        print("   4. 配置文件: amazon_countries_config.json")
        return True
    else:
        print("\n❌ 部分测试失败，请检查配置和依赖。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
