#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI版最终品牌商标搜索工具 - 10线程异步处理
结合专利1.py的界面风格和final_brand_scraper.py的反检测技术
"""

import pandas as pd
import requests
from bs4 import BeautifulSoup
import re
import unicodedata
import time
import random
import urllib.parse
import logging
import os
import json
import hashlib
import threading
from datetime import datetime
import brotli
import gzip
import concurrent.futures
import math
import sys
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import platform
import ctypes

# 123Proxy代理配置
http_proxy = "http://u1856561711670614:<EMAIL>:36931"
https_proxy = "http://u1856561711670614:<EMAIL>:36931"
url = "https://ifconfig.me"

proxySettings = {
    "http": http_proxy,
    "https": https_proxy,
}

# 并发请求线程数
num_threads = 5
# 每个线程隔多长时间切换代理IP， 建议15 - 60秒
session_alive = 15

threads = []

def make_requests():
    session = requests.Session()
    switch_time = time.time() + session_alive  # Set end time for session
    while True:
        #后台直接控制IP切换
        #if time.time() > switch_time:
        #    session.close()
        #    session = requests.Session()
        #    switch_time = time.time() + session_alive  # Set end time for session
        try:
            response = session.get(url, proxies=proxySettings)
            print(response.text)
        except Exception as e:
            #print(f"RequestException occurred: {str(e)}")
            session.close()
            session = requests.Session()
            continue

# 尝试导入curl_cffi用于TLS指纹伪造
try:
    import curl_cffi.requests as cf_requests
    CURL_CFFI_AVAILABLE = True
    print("✅ curl_cffi可用 - 将使用TLS指纹伪造")
except ImportError:
    CURL_CFFI_AVAILABLE = False
    print("⚠️ curl_cffi不可用 - 将使用标准requests")

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('gui_final_brand_scraper_fixed.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 全局暂停控制
PAUSED = False
pause_lock = threading.Lock()

# 添加一个全局锁，用于同步文件的写入
file_lock = threading.Lock()

# 内置UI主题，避免依赖外部文件
class AmazonUITheme:
    """亚马逊产品分析工具统一界面主题"""
    
    # 颜色方案
    COLORS = {
        "primary": "#356cac",     # 深蓝色 - 主色
        "secondary": "#4a90e2",   # 亮蓝色 - 次要色
        "accent": "#f89406",      # 橙色 - 强调色
        "background": "#f5f5f5",  # 浅灰色 - 背景色
        "text": "#333333",        # 深灰色 - 文本色
        "light_text": "#666666",  # 中灰色 - 次要文本
        "border": "#dddddd",      # 边框色
        "success": "#28a745",     # 成功色
        "warning": "#ffc107",     # 警告色
        "error": "#dc3545",       # 错误色
        "white": "#ffffff",       # 白色
        "light_gray": "#f0f0f0"   # 更浅的灰色
    }
    
    # 字体设置
    FONTS = {
        "title": ("微软雅黑", 12, "bold"),
        "subtitle": ("微软雅黑", 11, "bold"),
        "body": ("微软雅黑", 10),
        "small": ("微软雅黑", 9),
        "menu": ("微软雅黑", 10),
        "button": ("微软雅黑", 10),
        "status": ("微软雅黑", 9)
    }
    
    # 尺寸和间距
    PADDING = {
        "frame": 15,      # 框架内边距
        "button": 8,      # 按钮内边距
        "widget": 5,      # 控件间距
        "section": 10,    # 区块间距
        "tiny": 2,        # 最小间距
    }
    
    @classmethod
    def setup_styles(cls):
        """设置通用ttk样式"""
        style = ttk.Style()
        
        # 设置全局主题
        try:
            style.theme_use("clam")  # 使用clam主题作为基础
        except:
            pass  # 如果主题不可用，使用默认主题
        
        # 背景配置
        style.configure("TFrame", background=cls.COLORS["background"])
        style.configure("TLabelframe", background=cls.COLORS["background"])
        style.configure("TLabelframe.Label", background=cls.COLORS["background"], 
                        foreground=cls.COLORS["primary"], font=cls.FONTS["subtitle"])
        
        # 按钮风格
        style.configure("TButton", 
                        background=cls.COLORS["primary"],
                        foreground=cls.COLORS["white"],
                        font=cls.FONTS["button"],
                        padding=cls.PADDING["button"])
        
        style.map("TButton",
                  background=[('active', cls.COLORS["secondary"]), 
                              ('disabled', cls.COLORS["border"])],
                  foreground=[('disabled', cls.COLORS["light_text"])])
        
        # 次要按钮风格
        style.configure("Secondary.TButton", 
                        background=cls.COLORS["secondary"],
                        foreground=cls.COLORS["white"])
        
        # 强调按钮风格
        style.configure("Accent.TButton", 
                        background=cls.COLORS["accent"],
                        foreground=cls.COLORS["white"])
        
        # 标签风格
        style.configure("TLabel", 
                        background=cls.COLORS["background"],
                        foreground=cls.COLORS["text"],
                        font=cls.FONTS["body"])
        
        # 标题标签风格
        style.configure("Title.TLabel", 
                        background=cls.COLORS["background"],
                        foreground=cls.COLORS["primary"],
                        font=cls.FONTS["title"])
        
        # 进度条风格
        style.configure("TProgressbar", 
                        background=cls.COLORS["primary"],
                        troughcolor=cls.COLORS["light_gray"])
        
        return style
    
    @classmethod
    def setup_window(cls, root, title, size="800x600", resizable=(True, True)):
        """设置窗口基本属性"""
        root.title(title)
        root.geometry(size)
        root.configure(bg=cls.COLORS["background"])
        root.resizable(resizable[0], resizable[1])
        return root

def check_pause_status():
    """检查是否暂停，如果暂停则等待继续"""
    global PAUSED
    paused_local = False
    with pause_lock:
        paused_local = PAUSED
    
    while paused_local:
        time.sleep(0.5)  # 暂停状态下每0.5秒检查一次
        with pause_lock:
            paused_local = PAUSED

def md5_encrypt(string):
    """MD5加密字符串"""
    md5 = hashlib.md5()
    md5.update(string.encode('utf-8'))
    return md5.hexdigest()

def normalize_brand(brand):
    """标准化品牌名称用于比较"""
    normalized = unicodedata.normalize('NFKD', brand)
    normalized = re.sub(r'[\u0300-\u036f]', '', normalized)
    normalized = re.sub(r'[^a-zA-Z]', '', normalized)
    return normalized.upper()

class FinalBrandScraperGUI:
    """GUI版最终品牌商标搜索器"""
    
    def __init__(self):
        self.root = tk.Tk()
        AmazonUITheme.setup_window(self.root, "最终版品牌商标搜索工具", "900x700")
        AmazonUITheme.setup_styles()
        
        # 初始化变量
        self.brands_df = None
        self.is_running = False
        self.thread_pool = None
        self.progress_var = tk.DoubleVar()
        self.status_var = tk.StringVar(value="就绪")
        self.processed_count = 0
        self.total_count = 0
        
        # 浏览器配置文件
        self.browser_profiles = [
            {
                'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'sec_ch_ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
                'sec_ch_ua_platform': '"Windows"',
                'accept_language': 'en-US,en;q=0.9',
            }
        ]
        
        self.setup_ui()
        logger.info("GUI版最终品牌商标搜索器初始化完成")
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding=AmazonUITheme.PADDING["frame"])
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 标题
        title_label = ttk.Label(main_frame, text="最终版品牌商标搜索工具", style="Title.TLabel")
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 文件选择框架
        file_frame = ttk.LabelFrame(main_frame, text="文件选择", padding=AmazonUITheme.PADDING["section"])
        file_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.file_path_var = tk.StringVar(value="Brand.xlsx")
        ttk.Label(file_frame, text="品牌文件:").grid(row=0, column=0, sticky=tk.W)
        ttk.Entry(file_frame, textvariable=self.file_path_var, width=50).grid(row=0, column=1, padx=(10, 10))
        ttk.Button(file_frame, text="浏览", command=self.browse_file).grid(row=0, column=2)
        
        # 设置框架
        settings_frame = ttk.LabelFrame(main_frame, text="处理设置", padding=AmazonUITheme.PADDING["section"])
        settings_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 线程数设置
        ttk.Label(settings_frame, text="线程数:").grid(row=0, column=0, sticky=tk.W)
        self.thread_count_var = tk.IntVar(value=10)
        thread_spinbox = ttk.Spinbox(settings_frame, from_=1, to=20, textvariable=self.thread_count_var, width=10)
        thread_spinbox.grid(row=0, column=1, padx=(10, 20), sticky=tk.W)
        

        
        # 控制按钮框架
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=3, column=0, columnspan=3, pady=(0, 10))
        
        self.start_button = ttk.Button(control_frame, text="开始处理", command=self.start_processing)
        self.start_button.grid(row=0, column=0, padx=(0, 10))
        
        self.pause_button = ttk.Button(control_frame, text="暂停", command=self.pause_processing, state=tk.DISABLED)
        self.pause_button.grid(row=0, column=1, padx=(0, 10))
        
        self.stop_button = ttk.Button(control_frame, text="停止", command=self.stop_processing, state=tk.DISABLED)
        self.stop_button.grid(row=0, column=2)
        
        # 进度框架
        progress_frame = ttk.LabelFrame(main_frame, text="处理进度", padding=AmazonUITheme.PADDING["section"])
        progress_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 进度条
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 5))
        
        # 状态标签
        self.status_label = ttk.Label(progress_frame, textvariable=self.status_var)
        self.status_label.grid(row=1, column=0, sticky=tk.W)
        
        # 统计信息框架
        stats_frame = ttk.LabelFrame(main_frame, text="统计信息", padding=AmazonUITheme.PADDING["section"])
        stats_frame.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 统计标签
        self.stats_text = tk.Text(stats_frame, height=8, width=80)
        scrollbar = ttk.Scrollbar(stats_frame, orient=tk.VERTICAL, command=self.stats_text.yview)
        self.stats_text.configure(yscrollcommand=scrollbar.set)
        self.stats_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        file_frame.columnconfigure(1, weight=1)
        progress_frame.columnconfigure(0, weight=1)
        stats_frame.columnconfigure(0, weight=1)
        stats_frame.rowconfigure(0, weight=1)

    def browse_file(self):
        """浏览选择文件"""
        filename = filedialog.askopenfilename(
            title="选择品牌Excel文件",
            filetypes=[("Excel files", "*.xlsx *.xls"), ("All files", "*.*")]
        )
        if filename:
            self.file_path_var.set(filename)

    def get_advanced_headers(self, url):
        """生成高级反检测请求头"""
        profile = random.choice(self.browser_profiles)

        headers = {
            'User-Agent': profile['user_agent'],
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': profile['accept_language'],
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'sec-ch-ua': profile['sec_ch_ua'],
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': profile['sec_ch_ua_platform'],
            'Cache-Control': 'max-age=0',
        }

        # 添加Referer（如果不是首次访问）
        if 'trademarkia.com' in url:
            headers['Referer'] = 'https://www.trademarkia.com/'

        return headers

    def make_request_with_cffi(self, url, headers=None):
        """使用curl_cffi发送请求（TLS指纹伪造），使用123Proxy代理"""
        try:
            response = cf_requests.get(
                url,
                headers=headers,
                proxies=proxySettings,
                timeout=30,
                impersonate="chrome120",
                verify=False
            )
            return response
        except Exception as e:
            logger.warning(f"curl_cffi请求失败: {str(e)}")
            return None

    def decompress_response(self, response, is_cffi=False):
        """智能解压响应内容"""
        try:
            # curl_cffi通常自动处理解压，直接使用text
            if is_cffi:
                return response.text

            # 对于标准requests，先尝试直接使用text
            try:
                text_content = response.text
                # 简单检查内容是否看起来像HTML/文本
                if '<html' in text_content.lower() or 'trademark' in text_content.lower():
                    return text_content
            except:
                pass

            # 如果直接使用text失败，再尝试手动解压
            content_encoding = response.headers.get('content-encoding', '').lower()

            if content_encoding == 'br':
                try:
                    return brotli.decompress(response.content).decode('utf-8')
                except Exception as e:
                    logger.debug(f"Brotli解压失败，使用原始内容: {str(e)}")
                    return response.text
            elif content_encoding == 'gzip':
                try:
                    return gzip.decompress(response.content).decode('utf-8')
                except Exception as e:
                    logger.debug(f"Gzip解压失败，使用原始内容: {str(e)}")
                    return response.text
            else:
                return response.text

        except Exception as e:
            logger.debug(f"内容处理失败，使用备用方案: {str(e)}")
            return response.text if hasattr(response, 'text') else str(response.content)

    def safe_request(self, url, max_retries=3):
        """安全的HTTP请求，包含重试和错误处理，使用123Proxy代理"""
        headers = self.get_advanced_headers(url)
        session = requests.Session()

        # 记录遇到的错误类型
        error_types = []

        for attempt in range(max_retries):
            try:
                # 随机延迟
                time.sleep(random.uniform(1, 3))

                # 优先使用curl_cffi
                if CURL_CFFI_AVAILABLE:
                    response = self.make_request_with_cffi(url, headers)
                    if response:
                        if response.status_code == 200:
                            content = self.decompress_response(response, is_cffi=True)

                            # 检查是否被Cloudflare拦截
                            if 'cloudflare' in content.lower() and 'checking your browser' in content.lower():
                                logger.warning(f"🛡️ 检测到Cloudflare保护页面，尝试重试 ({attempt + 1}/{max_retries})")
                                error_types.append("cloudflare_protection")
                                continue

                            return content
                        elif response.status_code == 403:
                            logger.warning(f"🚫 403 Forbidden 错误，尝试重试 ({attempt + 1}/{max_retries})")
                            error_types.append("403_forbidden")
                        else:
                            logger.warning(f"⚠️ HTTP {response.status_code}: {url} (尝试 {attempt + 1}/{max_retries})")
                            error_types.append(f"http_{response.status_code}")

                # 备用：使用标准requests with 123Proxy
                response = session.get(url, headers=headers, proxies=proxySettings, timeout=30)

                if response.status_code == 200:
                    content = self.decompress_response(response, is_cffi=False)

                    # 检查Cloudflare保护
                    if 'cloudflare' in content.lower() and 'checking your browser' in content.lower():
                        logger.warning(f"🛡️ 检测到Cloudflare保护页面，尝试重试 ({attempt + 1}/{max_retries})")
                        error_types.append("cloudflare_protection")
                        continue

                    return content
                elif response.status_code == 403:
                    logger.warning(f"🚫 403 Forbidden 错误，尝试重试 ({attempt + 1}/{max_retries})")
                    error_types.append("403_forbidden")
                else:
                    logger.warning(f"⚠️ HTTP {response.status_code}: {url} (尝试 {attempt + 1}/{max_retries})")
                    error_types.append(f"http_{response.status_code}")

            except Exception as e:
                logger.warning(f"⚠️ 请求失败 (尝试 {attempt + 1}/{max_retries}): {str(e)}")
                error_types.append(f"exception_{type(e).__name__}")

                if attempt < max_retries - 1:
                    time.sleep(random.uniform(2, 4))

        # 所有重试都失败了
        error_summary = ", ".join(set(error_types))
        logger.error(f"❌ 所有重试失败: {url} (错误类型: {error_summary})")

        # 返回特殊标记，包含错误信息
        return f"REQUEST_FAILED:{error_summary}"

    def extract_trademark_count_from_title(self, html_content):
        """从页面标题中提取商标数量"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            title = soup.find('title')

            if title:
                title_text = title.get_text()
                # 查找类似 "3,751 Trademark Results found for" 的模式
                count_match = re.search(r'(\d{1,3}(?:,\d{3})*)\s+Trademark\s+Results?\s+found', title_text, re.IGNORECASE)
                if count_match:
                    count_str = count_match.group(1).replace(',', '')
                    return int(count_str)

            return 0
        except Exception as e:
            logger.warning(f"⚠️ 提取商标数量失败: {str(e)}")
            return 0

    def search_trademarks(self, brand_name):
        """搜索商标信息 - 只返回真实的商标数据"""
        try:
            # 构建搜索URL
            encoded_brand = urllib.parse.quote(brand_name)
            search_url = f"https://www.trademarkia.com/search/trademarks?query={encoded_brand}&reset_page=true&country=us"

            # 发送请求
            html_content = self.safe_request(search_url)

            if not html_content:
                logger.warning(f"⚠️ 无法获取 {brand_name} 的页面内容 - 标记为超时")
                return "TIMEOUT"  # 返回特殊标记表示超时

            # 检查是否为请求失败标记
            if isinstance(html_content, str) and html_content.startswith("REQUEST_FAILED:"):
                error_info = html_content.replace("REQUEST_FAILED:", "")
                logger.warning(f"⚠️ 品牌 {brand_name} 请求失败 ({error_info}) - 标记为超时")
                return "TIMEOUT"

            # 提取真实的商标数据
            trademarks = self.extract_real_trademark_data(brand_name, html_content)

            if trademarks and len(trademarks) > 0:
                logger.info(f"✅ 为品牌 {brand_name} 提取到 {len(trademarks)} 个真实商标")
                return trademarks
            elif trademarks == "ERROR_PAGE":
                # 检测到错误页面，无法确定是否真的没有商标
                logger.warning(f"⚠️ 品牌 {brand_name} 遇到错误页面 - 标记为超时")
                return "TIMEOUT"
            else:
                logger.info(f"ℹ️ 品牌 {brand_name} 确认无商标注册")
                return []  # 确认没有商标，返回空列表

        except Exception as e:
            logger.error(f"❌ 搜索商标失败 {brand_name}: {str(e)} - 标记为超时")
            return "TIMEOUT"

    def extract_real_trademark_data(self, brand_name, html_content):
        """从HTML内容中提取真实的商标信息 (完全基于wipo (US).py的成功逻辑)"""
        if not html_content:
            return []

        try:
            soup = BeautifulSoup(html_content, 'html.parser')

            # 检查错误页面
            table_rows = soup.select('a.table-row.tmTable') or soup.select('a.table-row') or soup.select('.tmTable.table-row')
            if not table_rows:
                error_texts = ['error', 'something went wrong', 'page not found', 'internal server error', '404', '500']
                for error in error_texts:
                    if error in html_content.lower():
                        logger.warning(f"检测到错误页面: '{error}' - 无法确定商标状态")
                        return "ERROR_PAGE"  # 返回特殊标记表示错误页面

            # 从h1.mr-auto获取商标数量
            trademark_count_value = 0
            count_heading = soup.find('h1', class_='mr-auto')
            if count_heading:
                count_text = count_heading.get_text()
                logger.info(f"数量标题文本: {count_text}")
                match = re.search(r'About (\d{1,3}(?:,\d{3})*)', count_text)
                if match:
                    trademark_count_value = int(match.group(1).replace(',', ''))
                    logger.info(f"提取到商标数量: {trademark_count_value}")
                else:
                    if 'found' in count_text.lower():
                        trademark_count_value = 0
                        logger.info("该品牌未找到商标")

            # 如果没有找到商标，提前返回
            if trademark_count_value == 0 and not table_rows:
                return []

            # 获取表格行
            logger.info("搜索表格行...")
            if not table_rows:
                table_rows = soup.select('a.table-row.tmTable')
            if not table_rows:
                table_rows = soup.select('a.table-row')
            if not table_rows:
                table_rows = soup.select('.tmTable.table-row')

            logger.info(f"找到 {len(table_rows)} 个商标条目")

            if not table_rows:
                logger.warning("未找到表格行！")
                return []

            trademarks = []

            # 提取每行数据
            for idx, row in enumerate(table_rows):
                logger.info(f"处理第 {idx+1}/{len(table_rows)} 行")

                try:
                    # 提取商标名称 (使用wipo (US).py的精确选择器)
                    name_element = row.select_one('p.font-bold.max-w-\\[300px\\]')
                    if name_element:
                        spans = name_element.find_all('span')
                        if spans:
                            trademark_name = ' '.join(span.get_text().strip() for span in spans)
                        else:
                            trademark_name = name_element.get_text().strip()
                    else:
                        name_element = row.select_one('td:nth-child(2) p.font-bold')
                        trademark_name = name_element.get_text().strip() if name_element else 'N/A'

                    logger.info(f"提取商标名称: {trademark_name}")
                except Exception as e:
                    logger.warning(f"提取商标名称失败: {str(e)}")
                    trademark_name = 'N/A'

                try:
                    # 提取持有者名称 (使用wipo (US).py的精确选择器)
                    owner_element = row.select_one('p.text-sm.font-normal')
                    if not owner_element:
                        owner_element = row.select_one('td.p-5 p.text-sm.font-normal')
                    owner_name = owner_element.get_text().strip() if owner_element else 'N/A'
                    logger.info(f"提取持有者名称: {owner_name}")
                except Exception as e:
                    logger.warning(f"提取持有者名称失败: {str(e)}")
                    owner_name = 'N/A'

                try:
                    # 提取商标编号 (使用wipo (US).py的逻辑)
                    tm_number_element = row.select_one('td.p-5 p.font-bold') or row.select_one('p.font-bold:not(.max-w-\\[300px\\])')
                    if tm_number_element:
                        tm_number = tm_number_element.get_text().strip()
                        if not re.match(r'^\d+$', tm_number):
                            tm_number_elements = row.select('p.font-bold')
                            if len(tm_number_elements) >= 4:
                                tm_number = tm_number_elements[3].get_text().strip()
                            else:
                                tm_number_element = row.select_one('td:nth-child(2) p.font-bold:nth-of-type(2)')
                                tm_number = tm_number_element.get_text().strip() if tm_number_element else 'N/A'
                    else:
                        tm_number_elements = row.select('p.font-bold')
                        if len(tm_number_elements) >= 4:
                            tm_number = tm_number_elements[3].get_text().strip()
                        else:
                            tm_number_element = row.select_one('td:nth-child(2) p.font-bold:nth-of-type(2)')
                            tm_number = tm_number_element.get_text().strip() if tm_number_element else 'N/A'

                    logger.info(f"提取商标编号: {tm_number}")
                except Exception as e:
                    logger.warning(f"提取商标编号失败: {str(e)}")
                    tm_number = 'N/A'

                try:
                    # 提取状态
                    status_element = row.select_one('.status-live-registered')
                    if not status_element:
                        status_element = row.select_one('td:nth-child(3) .flex:nth-child(1) span')
                    status = status_element.get_text().strip() if status_element else 'N/A'
                    logger.info(f"提取状态: {status}")
                except Exception as e:
                    logger.warning(f"提取状态失败: {str(e)}")
                    status = 'N/A'

                # 创建真实商标记录
                trademark = {
                    'brand_name': brand_name,
                    'trademark_text': trademark_name,
                    'owner': owner_name,
                    'serial_number': tm_number,
                    'status': status,
                    'is_live': 'Live' in status or 'Registered' in status,
                    'trademark_count': trademark_count_value
                }

                trademarks.append(trademark)
                logger.info(f"成功提取商标 {idx+1}: {trademark_name} - {owner_name} ({status})")

            logger.info(f"✅ 成功提取 {len(trademarks)} 个真实商标信息")
            return trademarks

        except Exception as e:
            logger.error(f"❌ 提取商标数据失败: {str(e)}")
            return []

    def create_simplified_trademarks(self, brand_name, count):
        """基于真实搜索数量创建简化商标记录 (回退方案)"""
        trademarks = []

        if count == 0:
            return []

        # 根据搜索结果数量确定创建的商标数量
        if count >= 100:
            # 大量结果 -> 创建5个商标记录 (模拟多个匹配)
            trademark_count = min(5, count)
            live_probability = 0.8
        elif count >= 50:
            # 较多结果 -> 创建4个商标记录
            trademark_count = min(4, count)
            live_probability = 0.7
        elif count >= 20:
            # 中等结果 -> 创建3个商标记录
            trademark_count = min(3, count)
            live_probability = 0.6
        elif count >= 5:
            # 少量结果 -> 创建2个商标记录
            trademark_count = min(2, count)
            live_probability = 0.5
        else:
            # 很少结果 -> 创建1个商标记录
            trademark_count = 1
            live_probability = 0.4

        # 创建商标记录
        for i in range(trademark_count):
            is_live = random.random() < live_probability

            trademark = {
                'brand_name': brand_name,
                'trademark_text': brand_name,  # 假设为完全匹配
                'owner': f"Unknown Owner {i+1}",  # 标记为未知持有者
                'serial_number': f"EST{random.randint(70000000, 99999999)}",  # EST = Estimated
                'status': 'Live/Registered' if is_live else 'Dead',
                'registration_date': 'Unknown',
                'expiration_date': 'Unknown',
                'class_info': 'Unknown',
                'is_live': is_live,
                'trademark_count': count,
                'data_source': 'simplified'  # 标记数据来源
            }

            trademarks.append(trademark)

        logger.info(f"📊 为品牌 {brand_name} 创建了 {trademark_count} 个基于数量的商标记录 (总搜索结果: {count})")
        return trademarks

    def calculate_b_value(self, brand_name, trademarks):
        """计算b值 - 基于完全匹配的商标数量 (与wipo (US).py逻辑一致)"""
        if not trademarks:
            return 0

        # 标准化品牌名称
        normalized_brand = normalize_brand(brand_name)
        logger.info(f'Normalized brand: {normalized_brand}')

        # 找出完全匹配的商标
        exact_matches = []
        for trademark in trademarks:
            trademark_name = trademark.get('trademark_text', '')
            if trademark_name:
                formatted_trademark = normalize_brand(trademark_name)
                if formatted_trademark == normalized_brand:
                    exact_matches.append({
                        'name': trademark_name,
                        'status': trademark.get('status', 'N/A')
                    })

        # 基于完全匹配数量计算b值
        match_count = len(exact_matches)
        logger.info(f"Found {match_count} exact matching trademarks")

        if match_count > 3:
            return 4
        elif match_count == 3:
            # 检查Live/Registered状态的数量
            live_or_registered_count = sum(1 for item in exact_matches
                                         if 'Live' in item['status'] or 'Registered' in item['status'])

            if live_or_registered_count >= 2:
                return 4
            else:
                return 3
        elif match_count == 2:
            return 2
        elif match_count == 1:
            return 1
        else:
            return 0

    def determine_final_category(self, b_value, trademarks):
        """根据b值和Live状态确定最终分类 (与wipo (US).py逻辑一致)"""
        if not trademarks:
            return 0  # 对应 0.xlsx

        # 统计Live/Registered状态的商标数量
        live_count = sum(1 for trademark in trademarks
                        if 'Live' in trademark.get('status', '') or 'Registered' in trademark.get('status', ''))

        logger.info(f"Found {live_count} live/registered trademarks out of {len(trademarks)} total")

        # 基于b值和live_count确定最终分类
        if b_value == 4:
            return 4  # 对应 4.xlsx
        elif b_value == 3:
            if live_count >= 2:
                return 2  # 对应 2.xlsx
            elif live_count == 1:
                return 1  # 对应 1.xlsx
            else:
                return 0  # 对应 0.xlsx
        elif b_value == 2 or b_value == 1:
            if live_count >= 1:
                return 1  # 对应 1.xlsx
            else:
                return 0  # 对应 0.xlsx
        else:  # b_value == 0
            return 0  # 对应 0.xlsx

    def process_brand(self, brand_data):
        """处理单个品牌"""
        try:
            # 安全地获取品牌名称，确保是字符串类型
            # 支持不同的列名格式：ASIN/asin, Brand/brand
            brand_name = str(brand_data.get('Brand', brand_data.get('brand', ''))).strip()
            asin = str(brand_data.get('ASIN', brand_data.get('asin', ''))).strip()

            if not brand_name:
                logger.warning(f"⚠️ 品牌名称为空，跳过: {asin}")
                return None

            # 搜索商标
            trademarks = self.search_trademarks(brand_name)

            # 检查是否为超时情况
            if trademarks == "TIMEOUT":
                logger.warning(f"⏰ 品牌 {brand_name} 超时，无法获取真实商标信息")
                return {
                    'b_value': None,
                    'final_category': 'timeout',
                    'brand_data': brand_data,
                    'trademarks': [],
                    'brand_name': brand_name,
                    'asin': asin,
                    'is_timeout': True,
                    'timeout_reason': '无法获取页面内容或遇到错误页面'
                }

            # 计算b值
            b_value = self.calculate_b_value(brand_name, trademarks)

            # 确定最终分类
            final_category = self.determine_final_category(b_value, trademarks)

            logger.info(f"品牌 {brand_name}: b={b_value}, 最终分类={final_category}")

            return {
                'b_value': b_value,
                'final_category': final_category,
                'brand_data': brand_data,
                'trademarks': trademarks,
                'brand_name': brand_name,
                'asin': asin,
                'is_timeout': False
            }

        except Exception as e:
            logger.error(f"❌ 处理品牌失败 {brand_name}: {str(e)}")
            return None

    def process_brand_subset(self, brands_subset, thread_id):
        """处理品牌子集 - 线程函数"""
        results = []

        for _, brand_data in brands_subset.iterrows():
            try:
                # 检查暂停状态
                check_pause_status()

                # 处理品牌
                result = self.process_brand(brand_data)
                if result:
                    results.append(result)

                # 更新进度
                self.processed_count += 1
                progress = (self.processed_count / self.total_count) * 100

                # 在主线程中更新GUI
                brand_name_display = str(brand_data.get('Brand', brand_data.get('brand', 'Unknown')))
                self.root.after(0, self.update_progress, progress, f"线程{thread_id}: 处理 {brand_name_display}")

                # 随机延迟
                time.sleep(random.uniform(1, 2))

            except Exception as e:
                logger.error(f"线程{thread_id}处理品牌失败: {str(e)}")
                continue

        return results

    def update_progress(self, progress, status):
        """更新进度条和状态"""
        self.progress_var.set(progress)
        self.status_var.set(status)

        # 更新统计信息
        stats_text = f"已处理: {self.processed_count}/{self.total_count} ({progress:.1f}%)\n"
        stats_text += f"当前状态: {status}\n"

        self.stats_text.delete(1.0, tk.END)
        self.stats_text.insert(tk.END, stats_text)
        self.stats_text.see(tk.END)

    def start_processing(self):
        """开始处理"""
        try:
            # 检查文件
            file_path = self.file_path_var.get()
            if not os.path.exists(file_path):
                messagebox.showerror("错误", f"文件不存在: {file_path}")
                return

            # 加载数据
            self.brands_df = pd.read_excel(file_path)
            logger.info(f"📖 从 {file_path} 加载了 {len(self.brands_df)} 个品牌")



            # 初始化计数器
            self.processed_count = 0
            self.total_count = len(self.brands_df)

            # 更新UI状态
            self.is_running = True
            self.start_button.config(state=tk.DISABLED)
            self.pause_button.config(state=tk.NORMAL)
            self.stop_button.config(state=tk.NORMAL)

            # 启动多线程处理
            self.start_multithreaded_processing()

        except Exception as e:
            logger.error(f"❌ 启动处理失败: {str(e)}")
            messagebox.showerror("错误", f"启动处理失败: {str(e)}")

    def start_multithreaded_processing(self):
        """启动多线程处理"""
        def run_processing():
            try:
                thread_count = self.thread_count_var.get()
                logger.info(f"🚀 启动 {thread_count} 个线程进行处理")

                # 分割数据
                brands_per_thread = math.ceil(len(self.brands_df) / thread_count)
                thread_data = []

                for i in range(thread_count):
                    start_idx = i * brands_per_thread
                    end_idx = min((i + 1) * brands_per_thread, len(self.brands_df))

                    if start_idx < len(self.brands_df):
                        subset = self.brands_df.iloc[start_idx:end_idx]
                        thread_data.append((subset, i + 1))

                # 使用ThreadPoolExecutor执行多线程处理
                all_results = []
                with concurrent.futures.ThreadPoolExecutor(max_workers=thread_count) as executor:
                    # 提交任务
                    future_to_thread = {
                        executor.submit(self.process_brand_subset, subset, thread_id): thread_id
                        for subset, thread_id in thread_data
                    }

                    # 收集结果
                    for future in concurrent.futures.as_completed(future_to_thread):
                        thread_id = future_to_thread[future]
                        try:
                            results = future.result()
                            all_results.extend(results)
                            logger.info(f"线程{thread_id}完成，处理了{len(results)}个品牌")
                        except Exception as e:
                            logger.error(f"线程{thread_id}执行失败: {str(e)}")

                # 保存结果
                self.save_results(all_results)

                # 更新UI
                self.root.after(0, self.processing_completed, len(all_results))

            except Exception as e:
                logger.error(f"❌ 多线程处理失败: {str(e)}")
                self.root.after(0, self.processing_failed, str(e))

        # 在后台线程中运行
        import threading
        processing_thread = threading.Thread(target=run_processing)
        processing_thread.daemon = True
        processing_thread.start()

    def save_results(self, results):
        """保存结果到Excel文件 (与wipo (US).py逻辑一致)"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # 按最终分类整理数据
            df_4_data = []  # 对应 4.xlsx
            df_2_data = []  # 对应 2.xlsx
            df_1_data = []  # 对应 1.xlsx
            df_0_data = []  # 对应 0.xlsx
            df_timeout_data = []  # 对应 timeout.xlsx (新增)

            for result in results:
                if result:
                    final_category = result['final_category']
                    b_value = result['b_value']

                    # 检查是否为超时情况
                    if result.get('is_timeout', False) or final_category == 'timeout':
                        # 准备超时数据
                        timeout_row = result['brand_data'].copy()
                        timeout_row['brand_name'] = result['brand_name']
                        timeout_row['asin'] = result['asin']
                        timeout_row['reason'] = result.get('timeout_reason', '无法获取真实商标信息')
                        timeout_row['timestamp'] = timestamp
                        timeout_row['status'] = '需要重新处理'
                        timeout_row['note'] = '因技术问题无法确定商标状态，建议人工复查或重新运行'
                        df_timeout_data.append(timeout_row)
                        logger.info(f"品牌 {result['brand_name']} 添加到超时表格 (原因: {timeout_row['reason']})")
                        continue

                    # 准备输出数据
                    output_row = result['brand_data'].copy()

                    # 添加wipo (US).py格式的字段
                    output_row['b'] = b_value
                    output_row['brand_name'] = result['brand_name']
                    output_row['asin'] = result['asin']

                    # 添加商标信息
                    if result['trademarks']:
                        # 统计Live商标数量
                        live_count = sum(1 for tm in result['trademarks']
                                       if 'Live' in tm.get('status', '') or 'Registered' in tm.get('status', ''))
                        output_row['live_count'] = live_count
                        output_row['total_trademarks'] = len(result['trademarks'])

                        # 添加前3个完全匹配商标的持有者信息
                        exact_matches = []
                        normalized_brand = normalize_brand(result['brand_name'])
                        for tm in result['trademarks']:
                            tm_name = tm.get('trademark_text', '')
                            if tm_name and normalize_brand(tm_name) == normalized_brand:
                                exact_matches.append(tm)

                        output_row['c1'] = exact_matches[0].get('owner', '') if len(exact_matches) > 0 else ''
                        output_row['c2'] = exact_matches[1].get('owner', '') if len(exact_matches) > 1 else ''
                        output_row['c3'] = exact_matches[2].get('owner', '') if len(exact_matches) > 2 else ''
                    else:
                        output_row['live_count'] = 0
                        output_row['total_trademarks'] = 0
                        output_row['c1'] = ''
                        output_row['c2'] = ''
                        output_row['c3'] = ''

                    # 根据最终分类分配到不同的数据集
                    if final_category == 4:
                        df_4_data.append(output_row)
                    elif final_category == 2:
                        df_2_data.append(output_row)
                    elif final_category == 1:
                        df_1_data.append(output_row)
                    else:  # final_category == 0
                        df_0_data.append(output_row)

            # 保存到不同的Excel文件 (与wipo (US).py一致)
            saved_files = []

            if df_4_data:
                filename_4 = f"4_{timestamp}.xlsx"
                df_4 = pd.DataFrame(df_4_data)
                df_4.to_excel(filename_4, index=False)
                saved_files.append(filename_4)
                logger.info(f"✅ 保存 {len(df_4_data)} 条记录到: {filename_4}")

            if df_2_data:
                filename_2 = f"2_{timestamp}.xlsx"
                df_2 = pd.DataFrame(df_2_data)
                df_2.to_excel(filename_2, index=False)
                saved_files.append(filename_2)
                logger.info(f"✅ 保存 {len(df_2_data)} 条记录到: {filename_2}")

            if df_1_data:
                filename_1 = f"1_{timestamp}.xlsx"
                df_1 = pd.DataFrame(df_1_data)
                df_1.to_excel(filename_1, index=False)
                saved_files.append(filename_1)
                logger.info(f"✅ 保存 {len(df_1_data)} 条记录到: {filename_1}")

            if df_0_data:
                filename_0 = f"0_{timestamp}.xlsx"
                df_0 = pd.DataFrame(df_0_data)
                df_0.to_excel(filename_0, index=False)
                saved_files.append(filename_0)
                logger.info(f"✅ 保存 {len(df_0_data)} 条记录到: {filename_0}")

            # 保存超时数据 (新增)
            if df_timeout_data:
                filename_timeout = f"timeout_{timestamp}.xlsx"
                df_timeout = pd.DataFrame(df_timeout_data)
                df_timeout.to_excel(filename_timeout, index=False)
                saved_files.append(filename_timeout)
                logger.info(f"⏰ 保存 {len(df_timeout_data)} 条超时记录到: {filename_timeout}")

            # 更新统计信息
            stats_text = f"处理完成！\n"
            stats_text += f"总处理数量: {len(results)}\n"
            stats_text += f"保存文件: {', '.join(saved_files)}\n\n"
            stats_text += "分类统计:\n"
            stats_text += f"  4.xlsx (高风险): {len(df_4_data)}\n"
            stats_text += f"  2.xlsx (中高风险): {len(df_2_data)}\n"
            stats_text += f"  1.xlsx (中等风险): {len(df_1_data)}\n"
            stats_text += f"  0.xlsx (低风险): {len(df_0_data)}\n"
            stats_text += f"  timeout.xlsx (超时): {len(df_timeout_data)}\n"

            self.root.after(0, lambda: self.stats_text.insert(tk.END, stats_text))

            return saved_files

        except Exception as e:
            logger.error(f"❌ 保存结果失败: {str(e)}")
            return None

    def pause_processing(self):
        """暂停/继续处理"""
        global PAUSED
        with pause_lock:
            PAUSED = not PAUSED

        if PAUSED:
            self.pause_button.config(text="继续")
            self.status_var.set("已暂停")
            logger.info("⏸️ 处理已暂停")
        else:
            self.pause_button.config(text="暂停")
            self.status_var.set("继续处理中...")
            logger.info("▶️ 处理已继续")

    def stop_processing(self):
        """停止处理"""
        global PAUSED
        with pause_lock:
            PAUSED = False

        self.is_running = False

        # 重置UI状态
        self.start_button.config(state=tk.NORMAL)
        self.pause_button.config(state=tk.DISABLED, text="暂停")
        self.stop_button.config(state=tk.DISABLED)

        self.status_var.set("已停止")
        logger.info("⏹️ 处理已停止")

    def processing_completed(self, result_count):
        """处理完成回调"""
        self.is_running = False

        # 重置UI状态
        self.start_button.config(state=tk.NORMAL)
        self.pause_button.config(state=tk.DISABLED, text="暂停")
        self.stop_button.config(state=tk.DISABLED)

        self.progress_var.set(100)
        self.status_var.set(f"处理完成！共处理 {result_count} 个品牌")

        messagebox.showinfo("完成", f"处理完成！共处理 {result_count} 个品牌")
        logger.info(f"🎉 处理完成！共处理 {result_count} 个品牌")

    def processing_failed(self, error_msg):
        """处理失败回调"""
        self.is_running = False

        # 重置UI状态
        self.start_button.config(state=tk.NORMAL)
        self.pause_button.config(state=tk.DISABLED, text="暂停")
        self.stop_button.config(state=tk.DISABLED)

        self.status_var.set("处理失败")

        messagebox.showerror("错误", f"处理失败: {error_msg}")
        logger.error(f"❌ 处理失败: {error_msg}")

    def run(self):
        """运行GUI"""
        self.root.mainloop()

def main():
    """主函数"""
    try:
        app = FinalBrandScraperGUI()
        app.run()
    except Exception as e:
        logger.error(f"❌ 程序启动失败: {str(e)}")
        messagebox.showerror("错误", f"程序启动失败: {str(e)}")

if __name__ == "__main__":
    main()
