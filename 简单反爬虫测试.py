#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单反爬虫测试 - 使用关键词"fan"
专注于基础功能测试，避免过度复杂化
"""

import requests
import time
import random
from bs4 import BeautifulSoup
import re

# 123Proxy代理配置
http_proxy = "http://u1856561711670614:<EMAIL>:36931"
https_proxy = "http://u1856561711670614:<EMAIL>:36931"

proxySettings = {
    "http": http_proxy,
    "https": https_proxy,
}

def get_simple_headers():
    """获取简单但有效的请求头"""
    user_agents = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:122.0) Gecko/20100101 Firefox/122.0',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2.1 Safari/605.1.15'
    ]
    
    return {
        'User-Agent': random.choice(user_agents),
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'DNT': '1',
        'Referer': 'https://www.google.com/'
    }

def test_proxy():
    """测试代理连接"""
    print("🔗 测试代理连接...")
    
    session = requests.Session()
    session.proxies = proxySettings
    session.headers.update(get_simple_headers())
    
    try:
        response = session.get("https://ifconfig.me/ip", timeout=15)
        if response.status_code == 200:
            ip = response.text.strip()
            print(f"✅ 代理连接成功，IP: {ip}")
            return session
        else:
            print(f"❌ 代理连接失败，状态码: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ 代理连接错误: {str(e)}")
        return None

def test_amazon_access(session):
    """测试Amazon访问"""
    print("\n🌐 测试Amazon访问...")
    
    # 先访问一个简单的页面
    test_urls = [
        "https://www.amazon.com/gp/help/customer/display.html",  # 帮助页面
        "https://www.amazon.com/gp/bestsellers",                # 畅销榜
        "https://www.amazon.com"                                # 首页
    ]
    
    for i, url in enumerate(test_urls, 1):
        print(f"  测试 {i}: {url}")
        
        try:
            # 随机延迟
            time.sleep(random.uniform(3, 6))
            
            response = session.get(url, timeout=20)
            
            if response.status_code == 200:
                content = response.text
                
                # 简单检查是否被拦截
                if any(keyword in content.lower() for keyword in ['captcha', 'robot check', 'blocked']):
                    print(f"    ⚠️ 可能被反爬虫拦截")
                    continue
                
                if 'amazon' in content.lower():
                    print(f"    ✅ 访问成功 (页面大小: {len(content)} 字符)")
                    return True
                else:
                    print(f"    ⚠️ 页面内容异常")
            else:
                print(f"    ❌ HTTP {response.status_code}")
                
        except Exception as e:
            print(f"    ❌ 请求错误: {str(e)}")
    
    return False

def test_search_fan(session):
    """测试搜索关键词'fan'"""
    print("\n🔍 测试搜索关键词 'fan'...")
    
    search_url = "https://www.amazon.com/s?k=fan"
    
    try:
        # 较长延迟
        print("  等待 5-8 秒...")
        time.sleep(random.uniform(5, 8))
        
        # 更新请求头
        session.headers.update(get_simple_headers())
        
        print(f"  发送搜索请求: {search_url}")
        response = session.get(search_url, timeout=30)
        
        print(f"  响应状态码: {response.status_code}")
        print(f"  响应大小: {len(response.text)} 字符")
        
        if response.status_code == 200:
            content = response.text
            
            # 检查反爬虫
            anti_bot_keywords = ['captcha', 'robot check', 'blocked', 'unusual traffic', 'verify you are human']
            detected_keywords = [kw for kw in anti_bot_keywords if kw in content.lower()]
            
            if detected_keywords:
                print(f"  ❌ 检测到反爬虫: {', '.join(detected_keywords)}")
                
                # 保存页面内容用于分析
                with open('blocked_page.html', 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"  📄 页面内容已保存到 blocked_page.html")
                
                return False
            
            # 检查是否为有效搜索结果
            if 's-result-item' in content or 'data-component-type="s-search-result"' in content:
                print("  ✅ 搜索成功，找到产品结果")
                
                # 简单解析产品数量
                soup = BeautifulSoup(content, 'html.parser')
                products = soup.find_all('div', {'data-component-type': 's-search-result'})
                
                if not products:
                    products = soup.find_all('div', class_='s-result-item')
                
                print(f"  📦 找到 {len(products)} 个产品")
                
                # 显示前3个产品标题
                for i, product in enumerate(products[:3], 1):
                    try:
                        title_elem = product.find('h2')
                        if title_elem:
                            title_link = title_elem.find('a')
                            if title_link:
                                title = title_link.get_text().strip()
                                print(f"    {i}. {title[:60]}...")
                    except:
                        print(f"    {i}. [解析失败]")
                
                return True
            else:
                print("  ⚠️ 未找到产品结果")
                
                # 检查是否有"无结果"提示
                if 'no results' in content.lower() or '没有找到' in content:
                    print("  ℹ️ 显示无搜索结果")
                else:
                    print("  ⚠️ 页面结构异常")
                
                return False
        else:
            print(f"  ❌ 搜索失败，状态码: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"  ❌ 搜索出错: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("🧪 简单反爬虫测试")
    print("关键词: fan")
    print("代理: 123Proxy")
    print("=" * 60)
    
    # 1. 测试代理
    session = test_proxy()
    if not session:
        print("\n❌ 代理测试失败，无法继续")
        return
    
    # 2. 测试Amazon访问
    amazon_ok = test_amazon_access(session)
    if not amazon_ok:
        print("\n❌ Amazon访问失败")
        return
    
    # 3. 测试搜索
    search_ok = test_search_fan(session)
    
    # 总结
    print("\n" + "=" * 60)
    if search_ok:
        print("🎉 测试成功!")
        print("✅ 代理连接正常")
        print("✅ Amazon访问成功")
        print("✅ 搜索功能正常")
        print("✅ 反爬虫绕过有效")
        
        print("\n💡 建议:")
        print("- 保持合理的请求频率")
        print("- 定期轮换User-Agent")
        print("- 使用随机延迟")
        print("- 避免过于频繁的请求")
        
    else:
        print("⚠️ 测试部分成功")
        print("✅ 代理连接正常")
        print("✅ Amazon访问成功")
        print("❌ 搜索功能受限")
        
        print("\n🔧 建议优化:")
        print("- 增加请求间隔时间")
        print("- 使用更多样化的请求头")
        print("- 考虑使用浏览器自动化")
        print("- 分析blocked_page.html了解拦截原因")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
