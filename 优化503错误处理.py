#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化503错误处理 - 专门针对Amazon 503错误的解决方案
使用关键词"fan"进行测试
"""

import requests
import time
import random
from bs4 import BeautifulSoup
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 123Proxy代理配置
http_proxy = "http://u1856561711670614:<EMAIL>:36931"
https_proxy = "http://u1856561711670614:<EMAIL>:36931"

proxySettings = {
    "http": http_proxy,
    "https": https_proxy,
}

class Amazon503Handler:
    """专门处理Amazon 503错误的采集器"""
    
    def __init__(self):
        self.session = None
        self.request_count = 0
        self.consecutive_503_count = 0
        self.last_success_time = time.time()
        self.setup_session()
    
    def setup_session(self):
        """设置会话"""
        if self.session:
            self.session.close()
        
        self.session = requests.Session()
        self.session.proxies = proxySettings
        
        # 更保守的请求头
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'DNT': '1'
        }
        
        self.session.headers.update(headers)
        
        # 设置超时和重试
        from requests.adapters import HTTPAdapter
        from urllib3.util.retry import Retry
        
        # 自定义重试策略 - 不自动重试503
        retry_strategy = Retry(
            total=0,  # 禁用自动重试，我们手动处理
            backoff_factor=0,
            status_forcelist=[],  # 不自动重试任何状态码
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
        
        logger.info("会话已重新设置")
    
    def smart_delay(self, base_delay=10):
        """智能延迟 - 根据503错误次数调整"""
        # 基础延迟
        delay = base_delay
        
        # 根据连续503错误次数增加延迟
        if self.consecutive_503_count > 0:
            delay = base_delay * (2 ** self.consecutive_503_count)  # 指数退避
            delay = min(delay, 300)  # 最大5分钟
        
        # 添加随机性
        delay += random.uniform(5, 15)
        
        logger.info(f"智能延迟 {delay:.1f} 秒 (连续503错误: {self.consecutive_503_count}次)")
        time.sleep(delay)
    
    def handle_503_error(self):
        """专门处理503错误"""
        self.consecutive_503_count += 1
        logger.warning(f"遇到503错误，连续第 {self.consecutive_503_count} 次")
        
        if self.consecutive_503_count >= 3:
            logger.info("连续503错误过多，执行深度恢复策略...")
            
            # 1. 重新设置会话
            self.setup_session()
            
            # 2. 长时间等待
            wait_time = random.uniform(180, 300)  # 3-5分钟
            logger.info(f"深度休息 {wait_time:.1f} 秒...")
            time.sleep(wait_time)
            
            # 3. 重置计数器
            self.consecutive_503_count = 0
            
            # 4. 预热会话
            self.warm_up_session()
        else:
            # 普通503处理
            wait_time = 60 * self.consecutive_503_count + random.uniform(30, 60)
            logger.info(f"等待 {wait_time:.1f} 秒后重试...")
            time.sleep(wait_time)
    
    def warm_up_session(self):
        """预热会话 - 访问安全页面"""
        logger.info("开始预热会话...")
        
        safe_urls = [
            "https://www.amazon.com/gp/help/customer/display.html?nodeId=508510",
            "https://www.amazon.com/gp/site-directory",
        ]
        
        for url in safe_urls:
            try:
                logger.info(f"预热访问: {url}")
                response = self.session.get(url, timeout=30)
                
                if response.status_code == 200:
                    logger.info("预热成功")
                    time.sleep(random.uniform(5, 10))
                    break
                else:
                    logger.warning(f"预热失败: {response.status_code}")
                    
            except Exception as e:
                logger.warning(f"预热错误: {str(e)}")
                continue
        
        logger.info("会话预热完成")
    
    def safe_request(self, url, max_retries=3):
        """安全请求 - 专门处理503"""
        for attempt in range(max_retries):
            try:
                logger.info(f"发送请求 (尝试 {attempt + 1}/{max_retries}): {url}")
                
                # 智能延迟
                if attempt > 0 or self.request_count > 0:
                    self.smart_delay()
                
                # 发送请求
                response = self.session.get(url, timeout=60)
                self.request_count += 1
                
                logger.info(f"响应状态码: {response.status_code}, 大小: {len(response.text)}")
                
                if response.status_code == 200:
                    # 成功 - 重置503计数器
                    self.consecutive_503_count = 0
                    self.last_success_time = time.time()
                    logger.info("请求成功!")
                    return response.text
                    
                elif response.status_code == 503:
                    logger.warning("遇到503错误 - Service Unavailable")
                    self.handle_503_error()
                    continue
                    
                elif response.status_code == 429:
                    logger.warning("遇到429错误 - Too Many Requests")
                    wait_time = random.uniform(120, 180)
                    logger.info(f"等待 {wait_time:.1f} 秒...")
                    time.sleep(wait_time)
                    continue
                    
                elif response.status_code == 403:
                    logger.warning("遇到403错误 - Forbidden")
                    self.setup_session()  # 重新设置会话
                    wait_time = random.uniform(60, 120)
                    logger.info(f"等待 {wait_time:.1f} 秒...")
                    time.sleep(wait_time)
                    continue
                    
                else:
                    logger.warning(f"未预期的状态码: {response.status_code}")
                    continue
                    
            except requests.exceptions.Timeout:
                logger.warning("请求超时")
                time.sleep(random.uniform(30, 60))
                continue
                
            except requests.exceptions.ConnectionError as e:
                logger.warning(f"连接错误: {str(e)}")
                time.sleep(random.uniform(30, 60))
                continue
                
            except Exception as e:
                logger.error(f"请求异常: {str(e)}")
                time.sleep(random.uniform(15, 30))
                continue
        
        logger.error(f"所有重试失败: {url}")
        return None
    
    def test_search_fan(self):
        """测试搜索关键词fan"""
        logger.info("开始测试搜索关键词: fan")
        
        # 1. 先测试代理
        logger.info("测试代理连接...")
        try:
            response = self.session.get("https://ifconfig.me/ip", timeout=15)
            if response.status_code == 200:
                ip = response.text.strip()
                logger.info(f"代理连接成功，IP: {ip}")
            else:
                logger.error("代理连接失败")
                return False
        except Exception as e:
            logger.error(f"代理测试错误: {str(e)}")
            return False
        
        # 2. 预热会话
        self.warm_up_session()
        
        # 3. 尝试搜索
        search_strategies = [
            "https://www.amazon.com/s?k=fan&ref=nb_sb_noss",
            "https://www.amazon.com/s?k=fan&i=aps",
            "https://www.amazon.com/s?k=fan&i=garden",
        ]
        
        for i, search_url in enumerate(search_strategies, 1):
            logger.info(f"尝试搜索策略 {i}: {search_url}")
            
            content = self.safe_request(search_url)
            
            if content:
                # 检查是否为有效搜索结果
                if self.analyze_search_results(content):
                    logger.info("搜索成功!")
                    return True
                else:
                    logger.warning("搜索结果无效")
            else:
                logger.warning(f"搜索策略 {i} 失败")
        
        logger.error("所有搜索策略都失败了")
        return False
    
    def analyze_search_results(self, content):
        """分析搜索结果"""
        try:
            # 检查反爬虫
            if any(keyword in content.lower() for keyword in ['captcha', 'robot check', 'blocked']):
                logger.warning("检测到反爬虫拦截")
                return False
            
            # 解析产品
            soup = BeautifulSoup(content, 'html.parser')
            products = soup.find_all('div', {'data-component-type': 's-search-result'})
            
            if not products:
                products = soup.find_all('div', class_='s-result-item')
            
            logger.info(f"找到 {len(products)} 个产品")
            
            if len(products) > 0:
                # 显示前几个产品
                for i, product in enumerate(products[:3], 1):
                    try:
                        title_elem = product.find('h2')
                        if title_elem:
                            title_link = title_elem.find('a')
                            if title_link:
                                title = title_link.get_text().strip()
                                logger.info(f"产品 {i}: {title[:50]}...")
                    except:
                        continue
                
                return True
            else:
                logger.warning("未找到产品结果")
                return False
                
        except Exception as e:
            logger.error(f"结果分析错误: {str(e)}")
            return False

def main():
    """主函数"""
    print("=" * 60)
    print("🛠️ Amazon 503错误优化处理")
    print("关键词: fan")
    print("策略: 智能延迟 + 指数退避 + 深度恢复")
    print("=" * 60)
    
    handler = Amazon503Handler()
    
    try:
        success = handler.test_search_fan()
        
        print("\n" + "=" * 60)
        if success:
            print("🎉 503错误处理成功!")
            print("✅ 成功绕过Amazon反爬虫")
            print("✅ 搜索功能正常工作")
        else:
            print("⚠️ 503错误处理部分成功")
            print("✅ 代理连接正常")
            print("❌ 搜索功能仍受限")
            
            print("\n💡 建议:")
            print("- Amazon搜索保护非常严格")
            print("- 可以尝试采集具体产品页面")
            print("- 或使用浏览器自动化方案")
            print("- 考虑通过分类页面获取产品")
        
        print("=" * 60)
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中发生错误: {str(e)}")

if __name__ == "__main__":
    main()
