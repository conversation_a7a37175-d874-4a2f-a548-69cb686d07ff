#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试价格.py的代理配置
"""

import requests
import threading
import time

# 从价格.py复制的代理配置
http_proxy = "http://u1856561711670614:<EMAIL>:36931"
https_proxy = "http://u1856561711670614:<EMAIL>:36931"
url = "https://ifconfig.me"

proxySettings = {
    "http": http_proxy,
    "https": https_proxy,
}

# 并发请求线程数
num_threads = 5
# 每个线程隔多长时间切换代理IP， 建议15 - 60秒
session_alive = 15

threads = []

def make_requests():
    session = requests.Session()
    switch_time = time.time() + session_alive  # Set end time for session
    while True:
        #后台直接控制IP切换
        #if time.time() > switch_time:
        #    session.close()
        #    session = requests.Session()
        #    switch_time = time.time() + session_alive  # Set end time for session
        try:
            response = session.get(url, proxies=proxySettings)
            print(f"价格模块获取IP: {response.text.strip()}")
            time.sleep(5)  # 每5秒请求一次
        except Exception as e:
            print(f"价格模块请求异常: {str(e)}")
            session.close()
            session = requests.Session()
            continue

def test_price_proxy():
    """测试价格.py的代理配置"""
    print("=" * 60)
    print("测试价格.py的123Proxy代理配置")
    print("=" * 60)
    print(f"代理服务器: proxy.123proxy.cn:36931")
    print(f"用户名: u1856561711670614")
    print("-" * 60)
    
    try:
        print("正在测试价格模块的代理连接...")
        session = requests.Session()
        
        # 测试连接
        response = session.get("https://ifconfig.me", proxies=proxySettings, timeout=15)
        
        if response.status_code == 200:
            ip = response.text.strip()
            print(f"✅ 价格模块代理连接成功!")
            print(f"当前IP地址: {ip}")
            
            # 测试多次请求
            print("\n测试连续3次请求...")
            ips = []
            for i in range(3):
                try:
                    resp = session.get("https://ifconfig.me", proxies=proxySettings, timeout=10)
                    if resp.status_code == 200:
                        current_ip = resp.text.strip()
                        ips.append(current_ip)
                        print(f"请求 {i+1}: {current_ip}")
                    time.sleep(2)
                except Exception as e:
                    print(f"请求 {i+1}: 失败 - {str(e)}")
            
            unique_ips = set(ips)
            print(f"\n获取到 {len(unique_ips)} 个不同IP:")
            for ip in unique_ips:
                print(f"  - {ip}")
            
            return True
            
        else:
            print(f"❌ 价格模块请求失败，HTTP状态码: {response.status_code}")
            return False
            
    except requests.exceptions.ProxyError as e:
        print(f"❌ 价格模块代理连接错误: {str(e)}")
        return False
        
    except requests.exceptions.Timeout as e:
        print(f"❌ 价格模块连接超时: {str(e)}")
        return False
        
    except Exception as e:
        print(f"❌ 价格模块其他错误: {str(e)}")
        return False

def test_concurrent_price_requests():
    """测试价格模块的并发请求"""
    print("\n" + "=" * 60)
    print("测试价格模块并发请求 (3个线程)")
    print("=" * 60)
    
    results = []
    
    def worker(thread_id):
        try:
            session = requests.Session()
            response = session.get("https://ifconfig.me", proxies=proxySettings, timeout=10)
            
            if response.status_code == 200:
                ip = response.text.strip()
                result = f"价格线程{thread_id}: {ip}"
                results.append(result)
                print(f"✅ {result}")
            else:
                result = f"价格线程{thread_id}: 失败 (状态码: {response.status_code})"
                results.append(result)
                print(f"❌ {result}")
                
        except Exception as e:
            result = f"价格线程{thread_id}: 错误 - {str(e)}"
            results.append(result)
            print(f"❌ {result}")
    
    threads = []
    for i in range(3):
        thread = threading.Thread(target=worker, args=(i+1,))
        threads.append(thread)
        thread.start()
    
    for thread in threads:
        thread.join()
    
    print(f"\n价格模块并发测试完成，共 {len(results)} 个结果")
    return len(results) > 0

if __name__ == "__main__":
    print("价格.py 123Proxy代理配置测试工具")
    print("使用与价格.py相同的代理配置进行测试\n")
    
    # 基础测试
    success = test_price_proxy()
    
    if success:
        # 并发测试
        test_concurrent_price_requests()
        
        print("\n" + "=" * 60)
        print("✅ 价格.py 代理配置测试完成!")
        print("价格模块已成功配置123Proxy代理")
        print("所有价格检查请求都会通过代理服务器")
        print("=" * 60)
    else:
        print("\n" + "=" * 60)
        print("❌ 价格.py 代理配置测试失败!")
        print("请检查网络连接和123Proxy账户状态")
        print("=" * 60)
