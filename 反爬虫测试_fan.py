#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
反爬虫测试脚本 - 使用关键词"fan"进行测试
测试强化版HTTP采集工具的反爬虫绕过能力
"""

import sys
import os
import time
import random
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from 采集_HTTP版 import AmazonHTTPScraper

def test_anti_bot_bypass():
    """测试反爬虫绕过功能"""
    print("=" * 60)
    print("🛡️ Amazon反爬虫绕过测试")
    print("测试关键词: fan")
    print("=" * 60)
    
    # 创建强化版采集器
    scraper = AmazonHTTPScraper(country='US', max_workers=1)
    
    # 1. 测试代理连接
    print("\n1. 测试123Proxy代理连接...")
    if not scraper.test_proxy_connection():
        print("❌ 代理连接失败，无法继续测试")
        return False
    
    # 2. 测试Amazon首页访问
    print("\n2. 测试Amazon首页访问...")
    try:
        content = scraper.safe_request("https://www.amazon.com")
        if content and len(content) > 1000:
            print("✅ Amazon首页访问成功")
            print(f"   页面大小: {len(content)} 字符")
            
            # 检查是否被拦截
            if scraper.is_blocked_response(content):
                print("⚠️ 首页访问被反爬虫拦截")
                return False
            else:
                print("✅ 首页访问未被拦截")
        else:
            print("❌ Amazon首页访问失败")
            return False
    except Exception as e:
        print(f"❌ 首页访问出错: {str(e)}")
        return False
    
    # 3. 测试搜索页面访问
    print("\n3. 测试搜索页面访问 (关键词: fan)...")
    try:
        search_url = "https://www.amazon.com/s?k=fan"
        print(f"搜索URL: {search_url}")
        
        content = scraper.safe_request(search_url)
        if content:
            print("✅ 搜索页面访问成功")
            print(f"   页面大小: {len(content)} 字符")
            
            # 检查是否被拦截
            if scraper.is_blocked_response(content):
                print("❌ 搜索页面被反爬虫拦截")
                print("   尝试分析拦截原因...")
                analyze_blocked_content(content)
                return False
            else:
                print("✅ 搜索页面未被拦截")
                
                # 分析搜索结果
                analyze_search_results(content)
                return True
        else:
            print("❌ 搜索页面访问失败")
            return False
            
    except Exception as e:
        print(f"❌ 搜索页面访问出错: {str(e)}")
        return False

def analyze_blocked_content(content):
    """分析被拦截的内容"""
    print("\n🔍 分析拦截内容:")
    
    if not content:
        print("   - 内容为空")
        return
    
    content_lower = content.lower()
    
    # 检查常见拦截指示器
    indicators = [
        ('robot check', '机器人检查'),
        ('captcha', '验证码'),
        ('cloudflare', 'Cloudflare防护'),
        ('checking your browser', '浏览器检查'),
        ('unusual traffic', '异常流量'),
        ('access denied', '访问被拒绝'),
        ('blocked', '被阻止'),
        ('security check', '安全检查'),
        ('verify you are human', '人机验证'),
        ('rate limit', '频率限制')
    ]
    
    found_indicators = []
    for indicator, description in indicators:
        if indicator in content_lower:
            found_indicators.append(description)
    
    if found_indicators:
        print(f"   - 检测到拦截指示器: {', '.join(found_indicators)}")
    else:
        print("   - 未检测到明显拦截指示器")
    
    # 检查页面长度
    print(f"   - 页面长度: {len(content)} 字符")
    
    # 检查是否包含Amazon元素
    amazon_elements = ['amazon', 'nav-logo', 'a-button', 's-result']
    amazon_found = [elem for elem in amazon_elements if elem in content_lower]
    
    if amazon_found:
        print(f"   - 包含Amazon元素: {', '.join(amazon_found)}")
    else:
        print("   - 缺少Amazon页面元素")

def analyze_search_results(content):
    """分析搜索结果"""
    print("\n📊 分析搜索结果:")
    
    from bs4 import BeautifulSoup
    
    try:
        soup = BeautifulSoup(content, 'html.parser')
        
        # 查找产品结果
        product_containers = soup.find_all('div', {'data-component-type': 's-search-result'})
        
        if not product_containers:
            # 尝试其他选择器
            product_containers = soup.find_all('div', class_='s-result-item')
        
        print(f"   - 找到产品数量: {len(product_containers)}")
        
        if product_containers:
            print("   - 前3个产品:")
            
            for i, container in enumerate(product_containers[:3], 1):
                try:
                    # 提取标题
                    title_elem = container.find('h2')
                    if title_elem:
                        title_link = title_elem.find('a')
                        if title_link:
                            title = title_link.get_text().strip()
                            print(f"     {i}. {title[:50]}...")
                        else:
                            print(f"     {i}. [无标题链接]")
                    else:
                        print(f"     {i}. [无标题]")
                        
                except Exception as e:
                    print(f"     {i}. [解析错误: {str(e)}]")
        
        # 检查是否有"没有结果"的提示
        no_results = soup.find(text=lambda text: text and 'no results' in text.lower())
        if no_results:
            print("   - ⚠️ 显示无搜索结果")
        
        # 检查分页
        pagination = soup.find('span', class_='s-pagination-strip')
        if pagination:
            print("   - ✅ 找到分页元素")
        else:
            print("   - ⚠️ 未找到分页元素")
            
    except Exception as e:
        print(f"   - ❌ 解析搜索结果出错: {str(e)}")

def test_multiple_requests():
    """测试多次请求的稳定性"""
    print("\n4. 测试多次请求稳定性...")
    
    scraper = AmazonHTTPScraper(country='US', max_workers=1)
    
    success_count = 0
    total_requests = 5
    
    for i in range(total_requests):
        print(f"\n   请求 {i+1}/{total_requests}:")
        
        try:
            # 随机延迟
            delay = random.uniform(3, 8)
            print(f"   等待 {delay:.1f} 秒...")
            time.sleep(delay)
            
            # 发送请求
            search_url = f"https://www.amazon.com/s?k=fan&page={i+1}"
            content = scraper.safe_request(search_url)
            
            if content and not scraper.is_blocked_response(content):
                print(f"   ✅ 请求 {i+1} 成功")
                success_count += 1
            else:
                print(f"   ❌ 请求 {i+1} 失败或被拦截")
                
        except Exception as e:
            print(f"   ❌ 请求 {i+1} 出错: {str(e)}")
    
    success_rate = (success_count / total_requests) * 100
    print(f"\n   📊 成功率: {success_count}/{total_requests} ({success_rate:.1f}%)")
    
    return success_rate >= 60  # 60%以上成功率认为通过

def main():
    """主测试函数"""
    print("🔬 Amazon反爬虫绕过能力测试")
    print("使用强化版HTTP采集工具")
    print("测试关键词: fan")
    print()
    
    try:
        # 基础反爬虫测试
        basic_success = test_anti_bot_bypass()
        
        if basic_success:
            print("\n✅ 基础反爬虫测试通过")
            
            # 多次请求稳定性测试
            stability_success = test_multiple_requests()
            
            if stability_success:
                print("\n🎉 所有反爬虫测试通过!")
                print("\n📋 测试总结:")
                print("✅ 代理连接正常")
                print("✅ Amazon首页访问成功")
                print("✅ 搜索页面访问成功")
                print("✅ 多次请求稳定")
                print("✅ 反爬虫绕过有效")
                
                print("\n🛡️ 反爬虫绕过特性:")
                print("- 智能请求头轮换")
                print("- 随机延迟策略")
                print("- 会话管理优化")
                print("- 多重重试机制")
                print("- 人类行为模拟")
                print("- 强化检测规避")
                
            else:
                print("\n⚠️ 稳定性测试未完全通过")
                print("建议进一步优化请求频率")
        else:
            print("\n❌ 基础反爬虫测试失败")
            print("需要进一步优化反爬虫策略")
        
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
    except Exception as e:
        print(f"\n\n测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
