#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试采集_HTTP版.py的代理配置和功能
"""

import requests
import threading
import time
from bs4 import BeautifulSoup

# 从采集_HTTP版.py复制的代理配置
http_proxy = "http://u1856561711670614:<EMAIL>:36931"
https_proxy = "http://u1856561711670614:<EMAIL>:36931"
url = "https://ifconfig.me"

proxySettings = {
    "http": http_proxy,
    "https": https_proxy,
}

# 并发请求线程数
num_threads = 5
# 每个线程隔多长时间切换代理IP， 建议15 - 60秒
session_alive = 15

threads = []

def make_requests():
    session = requests.Session()
    switch_time = time.time() + session_alive  # Set end time for session
    while True:
        #后台直接控制IP切换
        #if time.time() > switch_time:
        #    session.close()
        #    session = requests.Session()
        #    switch_time = time.time() + session_alive  # Set end time for session
        try:
            response = session.get(url, proxies=proxySettings)
            print(f"HTTP采集模块获取IP: {response.text.strip()}")
            time.sleep(5)  # 每5秒请求一次
        except Exception as e:
            print(f"HTTP采集模块请求异常: {str(e)}")
            session.close()
            session = requests.Session()
            continue

def test_http_scraper_proxy():
    """测试HTTP采集器的代理配置"""
    print("=" * 60)
    print("测试采集_HTTP版.py的123Proxy代理配置")
    print("=" * 60)
    print(f"代理服务器: proxy.123proxy.cn:36931")
    print(f"用户名: u1856561711670614")
    print("-" * 60)
    
    try:
        print("正在测试HTTP采集模块的代理连接...")
        session = requests.Session()
        session.proxies = proxySettings
        
        # 添加请求头
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Connection': 'keep-alive',
        })
        
        # 测试基础连接
        response = session.get("https://ifconfig.me", timeout=15)
        
        if response.status_code == 200:
            ip = response.text.strip()
            print(f"✅ HTTP采集模块代理连接成功!")
            print(f"当前IP地址: {ip}")
            
            # 测试Amazon连接
            print("\n测试Amazon网站HTTP连接...")
            try:
                amazon_response = session.get("https://www.amazon.com", timeout=15)
                if amazon_response.status_code == 200:
                    print(f"✅ Amazon HTTP连接成功 (状态码: {amazon_response.status_code})")
                    
                    # 检查是否被反爬虫拦截
                    if 'robot' in amazon_response.text.lower() or 'captcha' in amazon_response.text.lower():
                        print("⚠️ 检测到Amazon反爬虫机制，可能需要更多请求头或延迟")
                    else:
                        print("✅ Amazon页面内容正常获取")
                        
                else:
                    print(f"⚠️ Amazon连接异常 (状态码: {amazon_response.status_code})")
            except Exception as e:
                print(f"❌ Amazon连接失败: {str(e)}")
            
            # 测试搜索页面
            print("\n测试Amazon搜索页面...")
            try:
                search_url = "https://www.amazon.com/s?k=wireless+headphones"
                search_response = session.get(search_url, timeout=15)
                if search_response.status_code == 200:
                    print(f"✅ Amazon搜索页面连接成功 (状态码: {search_response.status_code})")
                    
                    # 简单解析搜索结果
                    soup = BeautifulSoup(search_response.content, 'html.parser')
                    products = soup.select('[data-component-type="s-search-result"]')
                    print(f"✅ 找到 {len(products)} 个搜索结果")
                    
                else:
                    print(f"⚠️ Amazon搜索页面异常 (状态码: {search_response.status_code})")
            except Exception as e:
                print(f"❌ Amazon搜索页面失败: {str(e)}")
            
            # 测试多次请求验证IP轮换
            print("\n测试连续3次IP请求...")
            ips = []
            for i in range(3):
                try:
                    resp = session.get("https://ifconfig.me", timeout=10)
                    if resp.status_code == 200:
                        current_ip = resp.text.strip()
                        ips.append(current_ip)
                        print(f"请求 {i+1}: {current_ip}")
                    time.sleep(2)
                except Exception as e:
                    print(f"请求 {i+1}: 失败 - {str(e)}")
            
            unique_ips = set(ips)
            print(f"\n获取到 {len(unique_ips)} 个不同IP:")
            for ip in unique_ips:
                print(f"  - {ip}")
            
            return True
            
        else:
            print(f"❌ HTTP采集模块请求失败，HTTP状态码: {response.status_code}")
            return False
            
    except requests.exceptions.ProxyError as e:
        print(f"❌ HTTP采集模块代理连接错误: {str(e)}")
        return False
        
    except requests.exceptions.Timeout as e:
        print(f"❌ HTTP采集模块连接超时: {str(e)}")
        return False
        
    except Exception as e:
        print(f"❌ HTTP采集模块其他错误: {str(e)}")
        return False

def test_concurrent_http_requests():
    """测试HTTP采集模块的并发请求"""
    print("\n" + "=" * 60)
    print("测试HTTP采集模块并发请求 (3个线程)")
    print("=" * 60)
    
    results = []
    
    def worker(thread_id):
        try:
            session = requests.Session()
            session.proxies = proxySettings
            session.headers.update({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            })
            
            response = session.get("https://ifconfig.me", timeout=10)
            
            if response.status_code == 200:
                ip = response.text.strip()
                result = f"HTTP采集线程{thread_id}: {ip}"
                results.append(result)
                print(f"✅ {result}")
            else:
                result = f"HTTP采集线程{thread_id}: 失败 (状态码: {response.status_code})"
                results.append(result)
                print(f"❌ {result}")
                
        except Exception as e:
            result = f"HTTP采集线程{thread_id}: 错误 - {str(e)}"
            results.append(result)
            print(f"❌ {result}")
    
    threads = []
    for i in range(3):
        thread = threading.Thread(target=worker, args=(i+1,))
        threads.append(thread)
        thread.start()
    
    for thread in threads:
        thread.join()
    
    print(f"\nHTTP采集模块并发测试完成，共 {len(results)} 个结果")
    return len(results) > 0

def test_amazon_scraping_simulation():
    """模拟Amazon采集功能测试"""
    print("\n" + "=" * 60)
    print("模拟Amazon产品采集功能测试")
    print("=" * 60)
    
    try:
        session = requests.Session()
        session.proxies = proxySettings
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Connection': 'keep-alive',
        })
        
        print("正在模拟产品搜索...")
        search_url = "https://www.amazon.com/s?k=bluetooth+speaker"
        
        response = session.get(search_url, timeout=15)
        
        if response.status_code == 200:
            print(f"✅ 搜索请求成功 (状态码: {response.status_code})")
            
            # 解析页面
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # 查找产品
            products = soup.select('[data-component-type="s-search-result"]')
            print(f"✅ 解析到 {len(products)} 个产品结果")
            
            # 提取前3个产品的基本信息
            for i, product in enumerate(products[:3], 1):
                try:
                    title_elem = product.select_one('h2 a span')
                    title = title_elem.get_text().strip() if title_elem else "未找到标题"
                    
                    price_elem = product.select_one('.a-price .a-offscreen')
                    price = price_elem.get_text().strip() if price_elem else "未找到价格"
                    
                    print(f"产品 {i}:")
                    print(f"  标题: {title[:50]}...")
                    print(f"  价格: {price}")
                    
                except Exception as e:
                    print(f"产品 {i}: 解析失败 - {str(e)}")
            
            print("✅ Amazon产品采集模拟测试成功")
            return True
            
        else:
            print(f"❌ 搜索请求失败 (状态码: {response.status_code})")
            return False
            
    except Exception as e:
        print(f"❌ Amazon采集模拟测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("采集_HTTP版.py 123Proxy代理配置测试工具")
    print("使用与采集_HTTP版.py相同的代理配置进行测试\n")
    
    # 基础测试
    success = test_http_scraper_proxy()
    
    if success:
        # 并发测试
        test_concurrent_http_requests()
        
        # Amazon采集模拟测试
        test_amazon_scraping_simulation()
        
        print("\n" + "=" * 60)
        print("✅ 采集_HTTP版.py 代理配置测试完成!")
        print("HTTP采集模块已成功配置123Proxy代理")
        print("所有HTTP请求都会通过代理服务器进行Amazon数据采集")
        print("不再需要浏览器自动化，直接使用HTTP请求")
        print("=" * 60)
    else:
        print("\n" + "=" * 60)
        print("❌ 采集_HTTP版.py 代理配置测试失败!")
        print("请检查网络连接和123Proxy账户状态")
        print("=" * 60)
