#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试采集.py的初始化是否正常
"""

import sys
import os

def test_amazon_scraper_init():
    """测试AmazonScraper类的初始化"""
    try:
        print("🔧 测试AmazonScraper类初始化...")
        
        # 导入必要的模块
        import tkinter as tk
        
        # 创建根窗口（但不显示）
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        
        # 尝试导入和初始化AmazonScraper
        sys.path.insert(0, os.getcwd())
        from 采集 import AmazonScraper
        
        print("✅ 模块导入成功")
        
        # 创建AmazonScraper实例
        scraper = AmazonScraper(root)
        print("✅ AmazonScraper初始化成功")
        
        # 检查关键属性
        if hasattr(scraper, 'log_message_queue'):
            print("✅ log_message_queue 已初始化")
        else:
            print("❌ log_message_queue 未初始化")
            return False
            
        if hasattr(scraper, 'countries'):
            print(f"✅ countries 已初始化，包含 {len(scraper.countries)} 个国家")
        else:
            print("❌ countries 未初始化")
            return False
            
        if hasattr(scraper, 'proxySettings'):
            print("✅ proxySettings 已初始化")
        else:
            print("❌ proxySettings 未初始化")
            return False
            
        if hasattr(scraper, 'session'):
            print("✅ session 已初始化")
        else:
            print("❌ session 未初始化")
            return False
        
        # 测试代理连接功能
        print("\n🔗 测试代理连接功能...")
        try:
            result = scraper.test_proxy_connection()
            if result:
                print("✅ 代理连接测试成功")
            else:
                print("⚠️ 代理连接测试失败（可能是网络问题）")
        except Exception as e:
            print(f"⚠️ 代理连接测试出错: {str(e)}")
        
        # 测试配置刷新功能
        print("\n🔄 测试配置刷新功能...")
        try:
            result = scraper.refresh_countries_config()
            if result:
                print("✅ 配置刷新成功")
            else:
                print("⚠️ 配置刷新失败")
        except Exception as e:
            print(f"⚠️ 配置刷新出错: {str(e)}")
        
        # 清理
        root.destroy()
        print("\n✅ 所有测试完成，初始化正常！")
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ 初始化错误: {str(e)}")
        return False

def main():
    """主函数"""
    print("🚀 开始测试采集.py初始化")
    print("=" * 50)
    
    success = test_amazon_scraper_init()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 测试通过！采集.py初始化正常。")
        print("\n💡 现在可以正常运行: python 采集.py")
    else:
        print("❌ 测试失败！请检查错误信息。")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
